<UserControl x:Class="AirMonitor.Views.ButtonStylesDemo"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             mc:Ignorable="d" 
             d:DesignHeight="800" d:DesignWidth="1000">

    <!-- ========================================
         FLUENT DESIGN BUTTON STYLES DEMO
         按钮样式演示页面
         ======================================== -->

    <ScrollViewer VerticalScrollBarVisibility="Auto" HorizontalScrollBarVisibility="Disabled">
        <StackPanel Margin="{StaticResource PageMargin}">
            <StackPanel.Resources>
                <Style TargetType="StackPanel">
                    <Setter Property="Margin" Value="0,0,0,24"/>
                </Style>
            </StackPanel.Resources>
            
            <!-- 页面标题 -->
            <TextBlock Text="Fluent Design Button Styles"
                       Style="{StaticResource Heading1Style}"
                       Margin="{StaticResource HeaderMargin}"/>
            
            <TextBlock Text="符合Microsoft Fluent Design System设计规范的完整按钮样式库"
                       Style="{StaticResource BodyStyle}"
                       Foreground="{StaticResource TextSecondaryBrush}"
                       Margin="{StaticResource SubHeaderMargin}"/>

            <!-- 主要按钮样式 -->
            <StackPanel>
                <TextBlock Text="Primary Buttons (主要按钮)"
                           Style="{StaticResource Heading2Style}"
                           Margin="{StaticResource HeaderMargin}"/>
                
                <WrapPanel Orientation="Horizontal"
                           ItemWidth="Auto"
                           ItemHeight="Auto">

                    <Button Content="Primary Button"
                            Style="{StaticResource PrimaryButtonStyle}"
                            Margin="0,0,8,8"/>
                    <Button Content="Disabled"
                            Style="{StaticResource PrimaryButtonStyle}"
                            IsEnabled="False"
                            Margin="0,0,8,8"/>
                    <Button Content="Small"
                            Style="{StaticResource SmallButtonStyle}"
                            Margin="0,0,8,8"/>
                    <Button Content="Large Button"
                            Style="{StaticResource LargeButtonStyle}"
                            Margin="0,0,8,8"/>
                </WrapPanel>
            </StackPanel>

            <!-- 次要按钮样式 -->
            <StackPanel>
                <TextBlock Text="Secondary Buttons (次要按钮)"
                           Style="{StaticResource Heading2Style}"
                           Margin="{StaticResource HeaderMargin}"/>

                <WrapPanel Orientation="Horizontal"
                           ItemWidth="Auto"
                           ItemHeight="Auto">

                    <Button Content="Secondary Button"
                            Style="{StaticResource SecondaryButtonStyle}"
                            Margin="0,0,8,8"/>
                    <Button Content="Disabled"
                            Style="{StaticResource SecondaryButtonStyle}"
                            IsEnabled="False"
                            Margin="0,0,8,8"/>
                    <Button Content="Outline"
                            Style="{StaticResource OutlineButtonStyle}"
                            Margin="0,0,8,8"/>
                </WrapPanel>
            </StackPanel>

            <!-- 文本按钮样式 -->
            <StackPanel>
                <TextBlock Text="Text Buttons (文本按钮)"
                           Style="{StaticResource Heading2Style}"
                           Margin="{StaticResource HeaderMargin}"/>

                <WrapPanel Orientation="Horizontal"
                           ItemWidth="Auto"
                           ItemHeight="Auto">

                    <Button Content="Text Button"
                            Style="{StaticResource TextButtonStyle}"
                            Margin="0,0,8,8"/>
                    <Button Content="Disabled"
                            Style="{StaticResource TextButtonStyle}"
                            IsEnabled="False"
                            Margin="0,0,8,8"/>
                    <Button Content="Link Style"
                            Style="{StaticResource TextButtonStyle}"
                            Margin="0,0,8,8"/>
                </WrapPanel>
            </StackPanel>

            <!-- 图标按钮样式 -->
            <StackPanel>
                <TextBlock Text="Icon Buttons (图标按钮)"
                           Style="{StaticResource Heading2Style}"
                           Margin="{StaticResource HeaderMargin}"/>

                <WrapPanel Orientation="Horizontal"
                           ItemWidth="Auto"
                           ItemHeight="Auto">

                    <Button Content="⚙"
                            Style="{StaticResource IconButtonStyle}"
                            Margin="0,0,8,8"/>
                    <Button Content="❤"
                            Style="{StaticResource IconButtonStyle}"
                            IsEnabled="False"
                            Margin="0,0,8,8"/>
                    <Button Content="🔍"
                            Style="{StaticResource IconButtonStyle}"
                            Margin="0,0,8,8"/>
                    <Button Content="+"
                            Style="{StaticResource CircularButtonStyle}"
                            Margin="0,0,8,8"/>
                </WrapPanel>
            </StackPanel>

            <!-- 浮动操作按钮 -->
            <StackPanel>
                <TextBlock Text="Floating Action Buttons (浮动操作按钮)"
                           Style="{StaticResource Heading2Style}"
                           Margin="{StaticResource HeaderMargin}"/>

                <WrapPanel Orientation="Horizontal"
                           ItemWidth="Auto"
                           ItemHeight="Auto">

                    <Button Content="+"
                            Style="{StaticResource FloatingActionButtonStyle}"
                            Margin="0,0,16,8"/>
                    <Button Content="✓"
                            Style="{StaticResource FloatingActionButtonStyle}"
                            IsEnabled="False"
                            Margin="0,0,16,8"/>
                    <Button Content="✉"
                            Style="{StaticResource FloatingActionButtonStyle}"
                            Margin="0,0,16,8"/>
                </WrapPanel>
            </StackPanel>

            <!-- 切换按钮 -->
            <StackPanel>
                <TextBlock Text="Toggle Buttons (切换按钮)"
                           Style="{StaticResource Heading2Style}"
                           Margin="{StaticResource HeaderMargin}"/>

                <WrapPanel Orientation="Horizontal"
                           ItemWidth="Auto"
                           ItemHeight="Auto">

                    <ToggleButton Content="Toggle"
                                  Style="{StaticResource ToggleButtonStyle}"
                                  Margin="0,0,8,8"/>
                    <ToggleButton Content="Checked"
                                  Style="{StaticResource ToggleButtonStyle}"
                                  IsChecked="True"
                                  Margin="0,0,8,8"/>
                    <ToggleButton Content="Disabled"
                                  Style="{StaticResource ToggleButtonStyle}"
                                  IsEnabled="False"
                                  Margin="0,0,8,8"/>
                </WrapPanel>
            </StackPanel>

            <!-- 语义化按钮 -->
            <StackPanel>
                <TextBlock Text="Semantic Buttons (语义化按钮)"
                           Style="{StaticResource Heading2Style}"
                           Margin="{StaticResource HeaderMargin}"/>

                <WrapPanel Orientation="Horizontal"
                           ItemWidth="Auto"
                           ItemHeight="Auto">
                    <Button Content="Success"
                            Style="{StaticResource SuccessButtonStyle}"
                            Margin="0,0,8,8"/>
                    <Button Content="Warning"
                            Style="{StaticResource WarningButtonStyle}"
                            Margin="0,0,8,8"/>
                    <Button Content="Error"
                            Style="{StaticResource ErrorButtonStyle}"
                            Margin="0,0,8,8"/>
                </WrapPanel>
            </StackPanel>

            <!-- 按钮组合示例 -->
            <StackPanel>
                <TextBlock Text="Button Groups (按钮组合)"
                           Style="{StaticResource Heading2Style}"
                           Margin="{StaticResource HeaderMargin}"/>
                
                <!-- 对话框按钮组 -->
                <Border Background="{StaticResource SurfaceBrush}"
                        BorderBrush="{StaticResource BorderBrush}"
                        BorderThickness="1"
                        CornerRadius="{StaticResource CardCornerRadius}"
                        Padding="{StaticResource CardPadding}">
                    <StackPanel>
                        <TextBlock Text="确认删除此项目？"
                                   Style="{StaticResource BodyStyle}"
                                   Margin="0,0,0,16"/>
                        <StackPanel Orientation="Horizontal"
                                    HorizontalAlignment="Right">
                            <Button Content="取消" Style="{StaticResource SecondaryButtonStyle}" Margin="0,0,8,0"/>
                            <Button Content="删除" Style="{StaticResource ErrorButtonStyle}"/>
                        </StackPanel>
                    </StackPanel>
                </Border>

                <!-- 工具栏按钮组 -->
                <Border Background="{StaticResource SurfaceBrush}"
                        BorderBrush="{StaticResource BorderBrush}"
                        BorderThickness="1"
                        CornerRadius="{StaticResource CardCornerRadius}"
                        Padding="8">
                    <StackPanel Orientation="Horizontal">
                        <Button Content="📁" Style="{StaticResource IconButtonStyle}" Margin="0,0,2,0"/>
                        <Button Content="💾" Style="{StaticResource IconButtonStyle}" Margin="0,0,2,0"/>
                        <Button Content="✂" Style="{StaticResource IconButtonStyle}" Margin="0,0,2,0"/>
                        <Button Content="📋" Style="{StaticResource IconButtonStyle}" Margin="0,0,4,0"/>
                        <Separator Style="{x:Null}" Width="1" Height="24"
                                   Background="{StaticResource BorderBrush}" Margin="4,0"/>
                        <Button Content="↶" Style="{StaticResource IconButtonStyle}" Margin="4,0,2,0"/>
                        <Button Content="↷" Style="{StaticResource IconButtonStyle}"/>
                    </StackPanel>
                </Border>
            </StackPanel>

            <!-- 使用说明 -->
            <StackPanel>
                <TextBlock Text="Usage Guidelines (使用指南)"
                           Style="{StaticResource Heading2Style}"
                           Margin="{StaticResource HeaderMargin}"/>

                <Border Background="{StaticResource BackgroundSecondaryBrush}"
                        BorderBrush="{StaticResource BorderSubtleBrush}"
                        BorderThickness="1"
                        CornerRadius="{StaticResource CardCornerRadius}"
                        Padding="{StaticResource CardPadding}">
                    <StackPanel>
                        <TextBlock Text="• Primary Button: 用于主要操作，每个界面最多使用一个"
                                   Style="{StaticResource BodyStyle}"
                                   Margin="0,0,0,8"/>
                        <TextBlock Text="• Secondary Button: 用于次要操作和取消操作"
                                   Style="{StaticResource BodyStyle}"
                                   Margin="0,0,0,8"/>
                        <TextBlock Text="• Text Button: 用于低优先级操作和链接"
                                   Style="{StaticResource BodyStyle}"
                                   Margin="0,0,0,8"/>
                        <TextBlock Text="• Icon Button: 用于工具栏和紧凑布局"
                                   Style="{StaticResource BodyStyle}"
                                   Margin="0,0,0,8"/>
                        <TextBlock Text="• Floating Action Button: 用于页面主要操作"
                                   Style="{StaticResource BodyStyle}"
                                   Margin="0,0,0,8"/>
                        <TextBlock Text="• Toggle Button: 用于开关状态切换"
                                   Style="{StaticResource BodyStyle}"/>
                    </StackPanel>
                </Border>
            </StackPanel>

        </StackPanel>
    </ScrollViewer>
</UserControl>
