<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <!-- ========================================
         FLUENT DESIGN BUTTON STYLES LIBRARY
         符合Microsoft Fluent Design System规范
         ======================================== -->

    <!-- ========================================
         BUTTON ANIMATIONS
         ======================================== -->

    <!-- 按钮悬停动画 -->
    <Storyboard x:Key="ButtonHoverAnimation">
        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleX)"
                         To="1.02" Duration="0:0:0.15">
            <DoubleAnimation.EasingFunction>
                <CubicEase EasingMode="EaseOut"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleY)"
                         To="1.02" Duration="0:0:0.15">
            <DoubleAnimation.EasingFunction>
                <CubicEase EasingMode="EaseOut"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
    </Storyboard>

    <!-- 按钮正常状态动画 -->
    <Storyboard x:Key="ButtonNormalAnimation">
        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleX)"
                         To="1" Duration="0:0:0.15">
            <DoubleAnimation.EasingFunction>
                <CubicEase EasingMode="EaseOut"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleY)"
                         To="1" Duration="0:0:0.15">
            <DoubleAnimation.EasingFunction>
                <CubicEase EasingMode="EaseOut"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
    </Storyboard>

    <!-- 按钮按下动画 -->
    <Storyboard x:Key="ButtonPressedAnimation">
        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleX)"
                         To="0.98" Duration="0:0:0.1">
            <DoubleAnimation.EasingFunction>
                <CubicEase EasingMode="EaseOut"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleY)"
                         To="0.98" Duration="0:0:0.1">
            <DoubleAnimation.EasingFunction>
                <CubicEase EasingMode="EaseOut"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
    </Storyboard>

    <!-- 焦点环动画 -->
    <Storyboard x:Key="FocusRingAnimation">
        <DoubleAnimation Storyboard.TargetProperty="Opacity"
                         From="0" To="1" Duration="0:0:0.2">
            <DoubleAnimation.EasingFunction>
                <CubicEase EasingMode="EaseOut"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
    </Storyboard>

    <!-- ========================================
         BASE BUTTON TEMPLATE
         基础按钮模板
         ======================================== -->

    <ControlTemplate x:Key="FluentButtonTemplate" TargetType="Button">
        <Grid>
            <!-- 焦点环 -->
            <Border x:Name="FocusRing"
                    BorderThickness="2"
                    BorderBrush="{StaticResource PrimaryBrush}"
                    CornerRadius="{StaticResource ButtonCornerRadius}"
                    Opacity="0"
                    Margin="-2"/>
            
            <!-- 主按钮容器 -->
            <Border x:Name="ButtonBorder"
                    Background="{TemplateBinding Background}"
                    BorderBrush="{TemplateBinding BorderBrush}"
                    BorderThickness="{TemplateBinding BorderThickness}"
                    CornerRadius="{StaticResource ButtonCornerRadius}"
                    Effect="{StaticResource Elevation1Shadow}">
                
                <ContentPresenter x:Name="ContentPresenter"
                                  HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                  VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                                  Margin="{TemplateBinding Padding}"
                                  Content="{TemplateBinding Content}"
                                  ContentTemplate="{TemplateBinding ContentTemplate}"
                                  ContentStringFormat="{TemplateBinding ContentStringFormat}"
                                  TextElement.Foreground="{TemplateBinding Foreground}"
                                  TextElement.FontFamily="{TemplateBinding FontFamily}"
                                  TextElement.FontSize="{TemplateBinding FontSize}"
                                  TextElement.FontWeight="{TemplateBinding FontWeight}"/>
            </Border>
        </Grid>

        <ControlTemplate.Triggers>
            <!-- 悬停状态 -->
            <Trigger Property="IsMouseOver" Value="True">
                <Trigger.EnterActions>
                    <BeginStoryboard Storyboard="{StaticResource ButtonHoverAnimation}"/>
                </Trigger.EnterActions>
                <Trigger.ExitActions>
                    <BeginStoryboard Storyboard="{StaticResource ButtonNormalAnimation}"/>
                </Trigger.ExitActions>
                <Setter TargetName="ButtonBorder" Property="Effect" Value="{StaticResource ButtonHoverShadow}"/>
            </Trigger>

            <!-- 按下状态 -->
            <Trigger Property="IsPressed" Value="True">
                <Trigger.EnterActions>
                    <BeginStoryboard Storyboard="{StaticResource ButtonPressedAnimation}"/>
                </Trigger.EnterActions>
                <Setter TargetName="ButtonBorder" Property="Effect" Value="{StaticResource ButtonPressedShadow}"/>
            </Trigger>

            <!-- 焦点状态 -->
            <Trigger Property="IsFocused" Value="True">
                <Trigger.EnterActions>
                    <BeginStoryboard Storyboard="{StaticResource FocusRingAnimation}" Storyboard.TargetName="FocusRing"/>
                </Trigger.EnterActions>
                <Setter TargetName="FocusRing" Property="Opacity" Value="1"/>
            </Trigger>

            <!-- 禁用状态 -->
            <Trigger Property="IsEnabled" Value="False">
                <Setter TargetName="ButtonBorder" Property="Opacity" Value="0.6"/>
                <Setter TargetName="ButtonBorder" Property="Effect" Value="{x:Null}"/>
            </Trigger>
        </ControlTemplate.Triggers>
    </ControlTemplate>

    <!-- ========================================
         PRIMARY BUTTON STYLE
         主要按钮样式
         ======================================== -->

    <Style x:Key="PrimaryButtonStyle" TargetType="Button">
        <Setter Property="Background" Value="{StaticResource PrimaryBrush}"/>
        <Setter Property="Foreground" Value="{StaticResource TextOnPrimaryBrush}"/>
        <Setter Property="BorderBrush" Value="{StaticResource PrimaryBrush}"/>
        <Setter Property="BorderThickness" Value="{StaticResource ButtonBorderThickness}"/>
        <Setter Property="Padding" Value="{StaticResource ButtonPadding}"/>
        <Setter Property="FontFamily" Value="{StaticResource BodyFontFamily}"/>
        <Setter Property="FontSize" Value="{StaticResource BodyFontSize}"/>
        <Setter Property="FontWeight" Value="{StaticResource BodyFontWeight}"/>
        <Setter Property="MinHeight" Value="32"/>
        <Setter Property="MinWidth" Value="80"/>
        <Setter Property="HorizontalContentAlignment" Value="Center"/>
        <Setter Property="VerticalContentAlignment" Value="Center"/>
        <Setter Property="Cursor" Value="Hand"/>
        <Setter Property="FocusVisualStyle" Value="{x:Null}"/>
        <Setter Property="RenderTransform">
            <Setter.Value>
                <ScaleTransform ScaleX="1" ScaleY="1"/>
            </Setter.Value>
        </Setter>
        <Setter Property="RenderTransformOrigin" Value="0.5,0.5"/>
        <Setter Property="Template" Value="{StaticResource FluentButtonTemplate}"/>
        
        <Style.Triggers>
            <!-- 悬停状态 -->
            <Trigger Property="IsMouseOver" Value="True">
                <Setter Property="Background" Value="{StaticResource PrimaryDarkBrush}"/>
                <Setter Property="BorderBrush" Value="{StaticResource PrimaryDarkBrush}"/>
            </Trigger>
            
            <!-- 按下状态 -->
            <Trigger Property="IsPressed" Value="True">
                <Setter Property="Background" Value="{StaticResource Primary800Brush}"/>
                <Setter Property="BorderBrush" Value="{StaticResource Primary800Brush}"/>
            </Trigger>
            
            <!-- 禁用状态 -->
            <Trigger Property="IsEnabled" Value="False">
                <Setter Property="Background" Value="{StaticResource Neutral300Brush}"/>
                <Setter Property="Foreground" Value="{StaticResource TextDisabledBrush}"/>
                <Setter Property="BorderBrush" Value="{StaticResource Neutral300Brush}"/>
            </Trigger>
        </Style.Triggers>
    </Style>

    <!-- ========================================
         SECONDARY BUTTON STYLE
         次要按钮样式
         ======================================== -->

    <Style x:Key="SecondaryButtonStyle" TargetType="Button">
        <Setter Property="Background" Value="{StaticResource SurfaceBrush}"/>
        <Setter Property="Foreground" Value="{StaticResource TextPrimaryBrush}"/>
        <Setter Property="BorderBrush" Value="{StaticResource BorderBrush}"/>
        <Setter Property="BorderThickness" Value="{StaticResource ButtonBorderThickness}"/>
        <Setter Property="Padding" Value="{StaticResource ButtonPadding}"/>
        <Setter Property="FontFamily" Value="{StaticResource BodyFontFamily}"/>
        <Setter Property="FontSize" Value="{StaticResource BodyFontSize}"/>
        <Setter Property="FontWeight" Value="{StaticResource BodyFontWeight}"/>
        <Setter Property="MinHeight" Value="32"/>
        <Setter Property="MinWidth" Value="80"/>
        <Setter Property="HorizontalContentAlignment" Value="Center"/>
        <Setter Property="VerticalContentAlignment" Value="Center"/>
        <Setter Property="Cursor" Value="Hand"/>
        <Setter Property="FocusVisualStyle" Value="{x:Null}"/>
        <Setter Property="RenderTransform">
            <Setter.Value>
                <ScaleTransform ScaleX="1" ScaleY="1"/>
            </Setter.Value>
        </Setter>
        <Setter Property="RenderTransformOrigin" Value="0.5,0.5"/>
        <Setter Property="Template" Value="{StaticResource FluentButtonTemplate}"/>
        
        <Style.Triggers>
            <!-- 悬停状态 -->
            <Trigger Property="IsMouseOver" Value="True">
                <Setter Property="Background" Value="{StaticResource Neutral100Brush}"/>
                <Setter Property="BorderBrush" Value="{StaticResource BorderStrongBrush}"/>
            </Trigger>
            
            <!-- 按下状态 -->
            <Trigger Property="IsPressed" Value="True">
                <Setter Property="Background" Value="{StaticResource Neutral200Brush}"/>
                <Setter Property="BorderBrush" Value="{StaticResource BorderStrongBrush}"/>
            </Trigger>
            
            <!-- 禁用状态 -->
            <Trigger Property="IsEnabled" Value="False">
                <Setter Property="Background" Value="{StaticResource Neutral100Brush}"/>
                <Setter Property="Foreground" Value="{StaticResource TextDisabledBrush}"/>
                <Setter Property="BorderBrush" Value="{StaticResource BorderSubtleBrush}"/>
            </Trigger>
        </Style.Triggers>
    </Style>

    <!-- ========================================
         TEXT BUTTON STYLE
         文本按钮样式
         ======================================== -->

    <Style x:Key="TextButtonStyle" TargetType="Button">
        <Setter Property="Background" Value="Transparent"/>
        <Setter Property="Foreground" Value="{StaticResource PrimaryBrush}"/>
        <Setter Property="BorderBrush" Value="Transparent"/>
        <Setter Property="BorderThickness" Value="0"/>
        <Setter Property="Padding" Value="{StaticResource ButtonPaddingSmall}"/>
        <Setter Property="FontFamily" Value="{StaticResource BodyFontFamily}"/>
        <Setter Property="FontSize" Value="{StaticResource BodyFontSize}"/>
        <Setter Property="FontWeight" Value="{StaticResource BodyFontWeight}"/>
        <Setter Property="MinHeight" Value="28"/>
        <Setter Property="MinWidth" Value="60"/>
        <Setter Property="HorizontalContentAlignment" Value="Center"/>
        <Setter Property="VerticalContentAlignment" Value="Center"/>
        <Setter Property="Cursor" Value="Hand"/>
        <Setter Property="FocusVisualStyle" Value="{x:Null}"/>
        <Setter Property="RenderTransform">
            <Setter.Value>
                <ScaleTransform ScaleX="1" ScaleY="1"/>
            </Setter.Value>
        </Setter>
        <Setter Property="RenderTransformOrigin" Value="0.5,0.5"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Grid>
                        <!-- 焦点环 -->
                        <Border x:Name="FocusRing"
                                BorderThickness="2"
                                BorderBrush="{StaticResource PrimaryBrush}"
                                CornerRadius="{StaticResource ButtonCornerRadius}"
                                Opacity="0"
                                Margin="-2"/>

                        <!-- 主按钮容器 -->
                        <Border x:Name="ButtonBorder"
                                Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="{StaticResource ButtonCornerRadius}">

                            <ContentPresenter x:Name="ContentPresenter"
                                              HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                              VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                                              Margin="{TemplateBinding Padding}"
                                              Content="{TemplateBinding Content}"
                                              ContentTemplate="{TemplateBinding ContentTemplate}"
                                              ContentStringFormat="{TemplateBinding ContentStringFormat}"
                                              TextElement.Foreground="{TemplateBinding Foreground}"
                                              TextElement.FontFamily="{TemplateBinding FontFamily}"
                                              TextElement.FontSize="{TemplateBinding FontSize}"
                                              TextElement.FontWeight="{TemplateBinding FontWeight}"/>
                        </Border>
                    </Grid>

                    <ControlTemplate.Triggers>
                        <!-- 悬停状态 -->
                        <Trigger Property="IsMouseOver" Value="True">
                            <Trigger.EnterActions>
                                <BeginStoryboard Storyboard="{StaticResource ButtonHoverAnimation}"/>
                            </Trigger.EnterActions>
                            <Trigger.ExitActions>
                                <BeginStoryboard Storyboard="{StaticResource ButtonNormalAnimation}"/>
                            </Trigger.ExitActions>
                            <Setter TargetName="ButtonBorder" Property="Background" Value="{StaticResource Primary50Brush}"/>
                        </Trigger>

                        <!-- 按下状态 -->
                        <Trigger Property="IsPressed" Value="True">
                            <Trigger.EnterActions>
                                <BeginStoryboard Storyboard="{StaticResource ButtonPressedAnimation}"/>
                            </Trigger.EnterActions>
                            <Setter TargetName="ButtonBorder" Property="Background" Value="{StaticResource Primary100Brush}"/>
                        </Trigger>

                        <!-- 焦点状态 -->
                        <Trigger Property="IsFocused" Value="True">
                            <Trigger.EnterActions>
                                <BeginStoryboard Storyboard="{StaticResource FocusRingAnimation}" Storyboard.TargetName="FocusRing"/>
                            </Trigger.EnterActions>
                            <Setter TargetName="FocusRing" Property="Opacity" Value="1"/>
                        </Trigger>

                        <!-- 禁用状态 -->
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter TargetName="ButtonBorder" Property="Opacity" Value="0.6"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>

        <Style.Triggers>
            <!-- 禁用状态 -->
            <Trigger Property="IsEnabled" Value="False">
                <Setter Property="Foreground" Value="{StaticResource TextDisabledBrush}"/>
            </Trigger>
        </Style.Triggers>
    </Style>

    <!-- ========================================
         ICON BUTTON STYLE
         图标按钮样式
         ======================================== -->

    <Style x:Key="IconButtonStyle" TargetType="Button">
        <Setter Property="Background" Value="Transparent"/>
        <Setter Property="Foreground" Value="{StaticResource TextSecondaryBrush}"/>
        <Setter Property="BorderBrush" Value="Transparent"/>
        <Setter Property="BorderThickness" Value="0"/>
        <Setter Property="Padding" Value="8"/>
        <Setter Property="Width" Value="40"/>
        <Setter Property="Height" Value="40"/>
        <Setter Property="HorizontalContentAlignment" Value="Center"/>
        <Setter Property="VerticalContentAlignment" Value="Center"/>
        <Setter Property="Cursor" Value="Hand"/>
        <Setter Property="FocusVisualStyle" Value="{x:Null}"/>
        <Setter Property="RenderTransform">
            <Setter.Value>
                <ScaleTransform ScaleX="1" ScaleY="1"/>
            </Setter.Value>
        </Setter>
        <Setter Property="RenderTransformOrigin" Value="0.5,0.5"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Grid>
                        <!-- 焦点环 -->
                        <Border x:Name="FocusRing"
                                BorderThickness="2"
                                BorderBrush="{StaticResource PrimaryBrush}"
                                CornerRadius="{StaticResource CornerRadiusCircular}"
                                Opacity="0"
                                Margin="-2"/>

                        <!-- 主按钮容器 -->
                        <Border x:Name="ButtonBorder"
                                Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="{StaticResource CornerRadiusCircular}">

                            <ContentPresenter x:Name="ContentPresenter"
                                              HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                              VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                                              Margin="{TemplateBinding Padding}"
                                              Content="{TemplateBinding Content}"
                                              ContentTemplate="{TemplateBinding ContentTemplate}"
                                              ContentStringFormat="{TemplateBinding ContentStringFormat}"
                                              TextElement.Foreground="{TemplateBinding Foreground}"
                                              TextElement.FontFamily="{TemplateBinding FontFamily}"
                                              TextElement.FontSize="{TemplateBinding FontSize}"
                                              TextElement.FontWeight="{TemplateBinding FontWeight}"/>
                        </Border>
                    </Grid>

                    <ControlTemplate.Triggers>
                        <!-- 悬停状态 -->
                        <Trigger Property="IsMouseOver" Value="True">
                            <Trigger.EnterActions>
                                <BeginStoryboard Storyboard="{StaticResource ButtonHoverAnimation}"/>
                            </Trigger.EnterActions>
                            <Trigger.ExitActions>
                                <BeginStoryboard Storyboard="{StaticResource ButtonNormalAnimation}"/>
                            </Trigger.ExitActions>
                            <Setter TargetName="ButtonBorder" Property="Background" Value="{StaticResource Neutral100Brush}"/>
                            <Setter Property="Foreground" Value="{StaticResource TextPrimaryBrush}"/>
                        </Trigger>

                        <!-- 按下状态 -->
                        <Trigger Property="IsPressed" Value="True">
                            <Trigger.EnterActions>
                                <BeginStoryboard Storyboard="{StaticResource ButtonPressedAnimation}"/>
                            </Trigger.EnterActions>
                            <Setter TargetName="ButtonBorder" Property="Background" Value="{StaticResource Neutral200Brush}"/>
                        </Trigger>

                        <!-- 焦点状态 -->
                        <Trigger Property="IsFocused" Value="True">
                            <Trigger.EnterActions>
                                <BeginStoryboard Storyboard="{StaticResource FocusRingAnimation}" Storyboard.TargetName="FocusRing"/>
                            </Trigger.EnterActions>
                            <Setter TargetName="FocusRing" Property="Opacity" Value="1"/>
                        </Trigger>

                        <!-- 禁用状态 -->
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter TargetName="ButtonBorder" Property="Opacity" Value="0.6"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>

        <Style.Triggers>
            <!-- 禁用状态 -->
            <Trigger Property="IsEnabled" Value="False">
                <Setter Property="Foreground" Value="{StaticResource TextDisabledBrush}"/>
            </Trigger>
        </Style.Triggers>
    </Style>

    <!-- ========================================
         FLOATING ACTION BUTTON STYLE
         浮动操作按钮样式
         ======================================== -->

    <Style x:Key="FloatingActionButtonStyle" TargetType="Button">
        <Setter Property="Background" Value="{StaticResource AccentBrush}"/>
        <Setter Property="Foreground" Value="{StaticResource TextOnAccentBrush}"/>
        <Setter Property="BorderBrush" Value="{StaticResource AccentBrush}"/>
        <Setter Property="BorderThickness" Value="0"/>
        <Setter Property="Padding" Value="16"/>
        <Setter Property="Width" Value="56"/>
        <Setter Property="Height" Value="56"/>
        <Setter Property="HorizontalContentAlignment" Value="Center"/>
        <Setter Property="VerticalContentAlignment" Value="Center"/>
        <Setter Property="Cursor" Value="Hand"/>
        <Setter Property="FocusVisualStyle" Value="{x:Null}"/>
        <Setter Property="RenderTransform">
            <Setter.Value>
                <ScaleTransform ScaleX="1" ScaleY="1"/>
            </Setter.Value>
        </Setter>
        <Setter Property="RenderTransformOrigin" Value="0.5,0.5"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Grid>
                        <!-- 焦点环 -->
                        <Border x:Name="FocusRing"
                                BorderThickness="2"
                                BorderBrush="{StaticResource AccentLightBrush}"
                                CornerRadius="{StaticResource CornerRadiusCircular}"
                                Opacity="0"
                                Margin="-2"/>

                        <!-- 主按钮容器 -->
                        <Border x:Name="ButtonBorder"
                                Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="{StaticResource CornerRadiusCircular}"
                                Effect="{StaticResource Elevation3Shadow}">

                            <ContentPresenter x:Name="ContentPresenter"
                                              HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                              VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                                              Margin="{TemplateBinding Padding}"
                                              Content="{TemplateBinding Content}"
                                              ContentTemplate="{TemplateBinding ContentTemplate}"
                                              ContentStringFormat="{TemplateBinding ContentStringFormat}"
                                              TextElement.Foreground="{TemplateBinding Foreground}"
                                              TextElement.FontFamily="{TemplateBinding FontFamily}"
                                              TextElement.FontSize="{TemplateBinding FontSize}"
                                              TextElement.FontWeight="{TemplateBinding FontWeight}"/>
                        </Border>
                    </Grid>

                    <ControlTemplate.Triggers>
                        <!-- 悬停状态 -->
                        <Trigger Property="IsMouseOver" Value="True">
                            <Trigger.EnterActions>
                                <BeginStoryboard Storyboard="{StaticResource ButtonHoverAnimation}"/>
                            </Trigger.EnterActions>
                            <Trigger.ExitActions>
                                <BeginStoryboard Storyboard="{StaticResource ButtonNormalAnimation}"/>
                            </Trigger.ExitActions>
                            <Setter TargetName="ButtonBorder" Property="Background" Value="{StaticResource AccentLightBrush}"/>
                            <Setter TargetName="ButtonBorder" Property="Effect" Value="{StaticResource Elevation4Shadow}"/>
                        </Trigger>

                        <!-- 按下状态 -->
                        <Trigger Property="IsPressed" Value="True">
                            <Trigger.EnterActions>
                                <BeginStoryboard Storyboard="{StaticResource ButtonPressedAnimation}"/>
                            </Trigger.EnterActions>
                            <Setter TargetName="ButtonBorder" Property="Background" Value="{StaticResource AccentDarkBrush}"/>
                            <Setter TargetName="ButtonBorder" Property="Effect" Value="{StaticResource Elevation2Shadow}"/>
                        </Trigger>

                        <!-- 焦点状态 -->
                        <Trigger Property="IsFocused" Value="True">
                            <Trigger.EnterActions>
                                <BeginStoryboard Storyboard="{StaticResource FocusRingAnimation}" Storyboard.TargetName="FocusRing"/>
                            </Trigger.EnterActions>
                            <Setter TargetName="FocusRing" Property="Opacity" Value="1"/>
                        </Trigger>

                        <!-- 禁用状态 -->
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter TargetName="ButtonBorder" Property="Opacity" Value="0.6"/>
                            <Setter TargetName="ButtonBorder" Property="Effect" Value="{StaticResource Elevation1Shadow}"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>

        <Style.Triggers>
            <!-- 禁用状态 -->
            <Trigger Property="IsEnabled" Value="False">
                <Setter Property="Background" Value="{StaticResource Neutral300Brush}"/>
                <Setter Property="Foreground" Value="{StaticResource TextDisabledBrush}"/>
                <Setter Property="BorderBrush" Value="{StaticResource Neutral300Brush}"/>
            </Trigger>
        </Style.Triggers>
    </Style>

    <!-- ========================================
         TOGGLE BUTTON STYLE
         切换按钮样式
         ======================================== -->

    <Style x:Key="ToggleButtonStyle" TargetType="ToggleButton">
        <Setter Property="Background" Value="{StaticResource SurfaceBrush}"/>
        <Setter Property="Foreground" Value="{StaticResource TextPrimaryBrush}"/>
        <Setter Property="BorderBrush" Value="{StaticResource BorderBrush}"/>
        <Setter Property="BorderThickness" Value="{StaticResource ButtonBorderThickness}"/>
        <Setter Property="Padding" Value="{StaticResource ButtonPadding}"/>
        <Setter Property="FontFamily" Value="{StaticResource BodyFontFamily}"/>
        <Setter Property="FontSize" Value="{StaticResource BodyFontSize}"/>
        <Setter Property="FontWeight" Value="{StaticResource BodyFontWeight}"/>
        <Setter Property="MinHeight" Value="32"/>
        <Setter Property="MinWidth" Value="80"/>
        <Setter Property="HorizontalContentAlignment" Value="Center"/>
        <Setter Property="VerticalContentAlignment" Value="Center"/>
        <Setter Property="Cursor" Value="Hand"/>
        <Setter Property="FocusVisualStyle" Value="{x:Null}"/>
        <Setter Property="RenderTransform">
            <Setter.Value>
                <ScaleTransform ScaleX="1" ScaleY="1"/>
            </Setter.Value>
        </Setter>
        <Setter Property="RenderTransformOrigin" Value="0.5,0.5"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="ToggleButton">
                    <Grid>
                        <!-- 焦点环 -->
                        <Border x:Name="FocusRing"
                                BorderThickness="2"
                                BorderBrush="{StaticResource PrimaryBrush}"
                                CornerRadius="{StaticResource ButtonCornerRadius}"
                                Opacity="0"
                                Margin="-2"/>

                        <!-- 主按钮容器 -->
                        <Border x:Name="ButtonBorder"
                                Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="{StaticResource ButtonCornerRadius}"
                                Effect="{StaticResource Elevation1Shadow}">

                            <ContentPresenter x:Name="ContentPresenter"
                                              HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                              VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                                              Margin="{TemplateBinding Padding}"
                                              Content="{TemplateBinding Content}"
                                              ContentTemplate="{TemplateBinding ContentTemplate}"
                                              ContentStringFormat="{TemplateBinding ContentStringFormat}"
                                              TextElement.Foreground="{TemplateBinding Foreground}"
                                              TextElement.FontFamily="{TemplateBinding FontFamily}"
                                              TextElement.FontSize="{TemplateBinding FontSize}"
                                              TextElement.FontWeight="{TemplateBinding FontWeight}"/>
                        </Border>
                    </Grid>

                    <ControlTemplate.Triggers>
                        <!-- 悬停状态 -->
                        <Trigger Property="IsMouseOver" Value="True">
                            <Trigger.EnterActions>
                                <BeginStoryboard Storyboard="{StaticResource ButtonHoverAnimation}"/>
                            </Trigger.EnterActions>
                            <Trigger.ExitActions>
                                <BeginStoryboard Storyboard="{StaticResource ButtonNormalAnimation}"/>
                            </Trigger.ExitActions>
                            <Setter TargetName="ButtonBorder" Property="Effect" Value="{StaticResource ButtonHoverShadow}"/>
                        </Trigger>

                        <!-- 按下状态 -->
                        <Trigger Property="IsPressed" Value="True">
                            <Trigger.EnterActions>
                                <BeginStoryboard Storyboard="{StaticResource ButtonPressedAnimation}"/>
                            </Trigger.EnterActions>
                            <Setter TargetName="ButtonBorder" Property="Effect" Value="{StaticResource ButtonPressedShadow}"/>
                        </Trigger>

                        <!-- 选中状态 -->
                        <Trigger Property="IsChecked" Value="True">
                            <Setter TargetName="ButtonBorder" Property="Background" Value="{StaticResource PrimaryBrush}"/>
                            <Setter TargetName="ButtonBorder" Property="BorderBrush" Value="{StaticResource PrimaryBrush}"/>
                            <Setter Property="Foreground" Value="{StaticResource TextOnPrimaryBrush}"/>
                        </Trigger>

                        <!-- 焦点状态 -->
                        <Trigger Property="IsFocused" Value="True">
                            <Trigger.EnterActions>
                                <BeginStoryboard Storyboard="{StaticResource FocusRingAnimation}" Storyboard.TargetName="FocusRing"/>
                            </Trigger.EnterActions>
                            <Setter TargetName="FocusRing" Property="Opacity" Value="1"/>
                        </Trigger>

                        <!-- 禁用状态 -->
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter TargetName="ButtonBorder" Property="Opacity" Value="0.6"/>
                            <Setter TargetName="ButtonBorder" Property="Effect" Value="{x:Null}"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>

        <Style.Triggers>
            <!-- 悬停状态（未选中） -->
            <MultiTrigger>
                <MultiTrigger.Conditions>
                    <Condition Property="IsMouseOver" Value="True"/>
                    <Condition Property="IsChecked" Value="False"/>
                </MultiTrigger.Conditions>
                <Setter Property="Background" Value="{StaticResource Neutral100Brush}"/>
                <Setter Property="BorderBrush" Value="{StaticResource BorderStrongBrush}"/>
            </MultiTrigger>

            <!-- 悬停状态（已选中） -->
            <MultiTrigger>
                <MultiTrigger.Conditions>
                    <Condition Property="IsMouseOver" Value="True"/>
                    <Condition Property="IsChecked" Value="True"/>
                </MultiTrigger.Conditions>
                <Setter Property="Background" Value="{StaticResource PrimaryDarkBrush}"/>
                <Setter Property="BorderBrush" Value="{StaticResource PrimaryDarkBrush}"/>
            </MultiTrigger>

            <!-- 按下状态（未选中） -->
            <MultiTrigger>
                <MultiTrigger.Conditions>
                    <Condition Property="IsPressed" Value="True"/>
                    <Condition Property="IsChecked" Value="False"/>
                </MultiTrigger.Conditions>
                <Setter Property="Background" Value="{StaticResource Neutral200Brush}"/>
                <Setter Property="BorderBrush" Value="{StaticResource BorderStrongBrush}"/>
            </MultiTrigger>

            <!-- 按下状态（已选中） -->
            <MultiTrigger>
                <MultiTrigger.Conditions>
                    <Condition Property="IsPressed" Value="True"/>
                    <Condition Property="IsChecked" Value="True"/>
                </MultiTrigger.Conditions>
                <Setter Property="Background" Value="{StaticResource Primary800Brush}"/>
                <Setter Property="BorderBrush" Value="{StaticResource Primary800Brush}"/>
            </MultiTrigger>

            <!-- 禁用状态 -->
            <Trigger Property="IsEnabled" Value="False">
                <Setter Property="Background" Value="{StaticResource Neutral100Brush}"/>
                <Setter Property="Foreground" Value="{StaticResource TextDisabledBrush}"/>
                <Setter Property="BorderBrush" Value="{StaticResource BorderSubtleBrush}"/>
            </Trigger>
        </Style.Triggers>
    </Style>

    <!-- ========================================
         BUTTON SIZE VARIATIONS
         按钮尺寸变体
         ======================================== -->

    <!-- 小尺寸按钮样式 -->
    <Style x:Key="SmallButtonStyle" TargetType="Button" BasedOn="{StaticResource PrimaryButtonStyle}">
        <Setter Property="Padding" Value="{StaticResource ButtonPaddingSmall}"/>
        <Setter Property="FontSize" Value="12"/>
        <Setter Property="MinHeight" Value="28"/>
        <Setter Property="MinWidth" Value="60"/>
    </Style>

    <!-- 大尺寸按钮样式 -->
    <Style x:Key="LargeButtonStyle" TargetType="Button" BasedOn="{StaticResource PrimaryButtonStyle}">
        <Setter Property="Padding" Value="{StaticResource ButtonPaddingLarge}"/>
        <Setter Property="FontSize" Value="16"/>
        <Setter Property="FontWeight" Value="SemiBold"/>
        <Setter Property="MinHeight" Value="44"/>
        <Setter Property="MinWidth" Value="120"/>
    </Style>

    <!-- ========================================
         SEMANTIC BUTTON STYLES
         语义化按钮样式
         ======================================== -->

    <!-- 成功按钮样式 -->
    <Style x:Key="SuccessButtonStyle" TargetType="Button" BasedOn="{StaticResource PrimaryButtonStyle}">
        <Setter Property="Background" Value="{StaticResource SuccessBrush}"/>
        <Setter Property="BorderBrush" Value="{StaticResource SuccessBrush}"/>

        <Style.Triggers>
            <Trigger Property="IsMouseOver" Value="True">
                <Setter Property="Background" Value="{StaticResource SuccessDarkBrush}"/>
                <Setter Property="BorderBrush" Value="{StaticResource SuccessDarkBrush}"/>
            </Trigger>

            <Trigger Property="IsPressed" Value="True">
                <Setter Property="Background" Value="{StaticResource SuccessDarkBrush}"/>
                <Setter Property="BorderBrush" Value="{StaticResource SuccessDarkBrush}"/>
            </Trigger>
        </Style.Triggers>
    </Style>

    <!-- 警告按钮样式 -->
    <Style x:Key="WarningButtonStyle" TargetType="Button" BasedOn="{StaticResource PrimaryButtonStyle}">
        <Setter Property="Background" Value="{StaticResource WarningBrush}"/>
        <Setter Property="BorderBrush" Value="{StaticResource WarningBrush}"/>
        <Setter Property="Foreground" Value="{StaticResource NeutralBlackBrush}"/>

        <Style.Triggers>
            <Trigger Property="IsMouseOver" Value="True">
                <Setter Property="Background" Value="{StaticResource WarningDarkBrush}"/>
                <Setter Property="BorderBrush" Value="{StaticResource WarningDarkBrush}"/>
                <Setter Property="Foreground" Value="{StaticResource NeutralWhiteBrush}"/>
            </Trigger>

            <Trigger Property="IsPressed" Value="True">
                <Setter Property="Background" Value="{StaticResource WarningDarkBrush}"/>
                <Setter Property="BorderBrush" Value="{StaticResource WarningDarkBrush}"/>
                <Setter Property="Foreground" Value="{StaticResource NeutralWhiteBrush}"/>
            </Trigger>
        </Style.Triggers>
    </Style>

    <!-- 错误按钮样式 -->
    <Style x:Key="ErrorButtonStyle" TargetType="Button" BasedOn="{StaticResource PrimaryButtonStyle}">
        <Setter Property="Background" Value="{StaticResource ErrorBrush}"/>
        <Setter Property="BorderBrush" Value="{StaticResource ErrorBrush}"/>

        <Style.Triggers>
            <Trigger Property="IsMouseOver" Value="True">
                <Setter Property="Background" Value="{StaticResource ErrorDarkBrush}"/>
                <Setter Property="BorderBrush" Value="{StaticResource ErrorDarkBrush}"/>
            </Trigger>

            <Trigger Property="IsPressed" Value="True">
                <Setter Property="Background" Value="{StaticResource ErrorDarkBrush}"/>
                <Setter Property="BorderBrush" Value="{StaticResource ErrorDarkBrush}"/>
            </Trigger>
        </Style.Triggers>
    </Style>

    <!-- ========================================
         SPECIALIZED BUTTON STYLES
         特殊按钮样式
         ======================================== -->

    <!-- 圆形按钮样式 -->
    <Style x:Key="CircularButtonStyle" TargetType="Button" BasedOn="{StaticResource PrimaryButtonStyle}">
        <Setter Property="Width" Value="48"/>
        <Setter Property="Height" Value="48"/>
        <Setter Property="Padding" Value="12"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Grid>
                        <!-- 焦点环 -->
                        <Border x:Name="FocusRing"
                                BorderThickness="2"
                                BorderBrush="{StaticResource PrimaryBrush}"
                                CornerRadius="{StaticResource CornerRadiusCircular}"
                                Opacity="0"
                                Margin="-2"/>

                        <!-- 主按钮容器 -->
                        <Border x:Name="ButtonBorder"
                                Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="{StaticResource CornerRadiusCircular}"
                                Effect="{StaticResource Elevation1Shadow}">

                            <ContentPresenter x:Name="ContentPresenter"
                                              HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                              VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                                              Margin="{TemplateBinding Padding}"
                                              Content="{TemplateBinding Content}"
                                              ContentTemplate="{TemplateBinding ContentTemplate}"
                                              ContentStringFormat="{TemplateBinding ContentStringFormat}"
                                              TextElement.Foreground="{TemplateBinding Foreground}"
                                              TextElement.FontFamily="{TemplateBinding FontFamily}"
                                              TextElement.FontSize="{TemplateBinding FontSize}"
                                              TextElement.FontWeight="{TemplateBinding FontWeight}"/>
                        </Border>
                    </Grid>

                    <ControlTemplate.Triggers>
                        <!-- 悬停状态 -->
                        <Trigger Property="IsMouseOver" Value="True">
                            <Trigger.EnterActions>
                                <BeginStoryboard Storyboard="{StaticResource ButtonHoverAnimation}"/>
                            </Trigger.EnterActions>
                            <Trigger.ExitActions>
                                <BeginStoryboard Storyboard="{StaticResource ButtonNormalAnimation}"/>
                            </Trigger.ExitActions>
                            <Setter TargetName="ButtonBorder" Property="Effect" Value="{StaticResource ButtonHoverShadow}"/>
                        </Trigger>

                        <!-- 按下状态 -->
                        <Trigger Property="IsPressed" Value="True">
                            <Trigger.EnterActions>
                                <BeginStoryboard Storyboard="{StaticResource ButtonPressedAnimation}"/>
                            </Trigger.EnterActions>
                            <Setter TargetName="ButtonBorder" Property="Effect" Value="{StaticResource ButtonPressedShadow}"/>
                        </Trigger>

                        <!-- 焦点状态 -->
                        <Trigger Property="IsFocused" Value="True">
                            <Trigger.EnterActions>
                                <BeginStoryboard Storyboard="{StaticResource FocusRingAnimation}" Storyboard.TargetName="FocusRing"/>
                            </Trigger.EnterActions>
                            <Setter TargetName="FocusRing" Property="Opacity" Value="1"/>
                        </Trigger>

                        <!-- 禁用状态 -->
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter TargetName="ButtonBorder" Property="Opacity" Value="0.6"/>
                            <Setter TargetName="ButtonBorder" Property="Effect" Value="{x:Null}"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- 轮廓按钮样式 -->
    <Style x:Key="OutlineButtonStyle" TargetType="Button" BasedOn="{StaticResource SecondaryButtonStyle}">
        <Setter Property="Background" Value="Transparent"/>
        <Setter Property="Foreground" Value="{StaticResource PrimaryBrush}"/>
        <Setter Property="BorderBrush" Value="{StaticResource PrimaryBrush}"/>
        <Setter Property="BorderThickness" Value="2"/>

        <Style.Triggers>
            <Trigger Property="IsMouseOver" Value="True">
                <Setter Property="Background" Value="{StaticResource Primary50Brush}"/>
                <Setter Property="BorderBrush" Value="{StaticResource PrimaryDarkBrush}"/>
                <Setter Property="Foreground" Value="{StaticResource PrimaryDarkBrush}"/>
            </Trigger>

            <Trigger Property="IsPressed" Value="True">
                <Setter Property="Background" Value="{StaticResource Primary100Brush}"/>
                <Setter Property="BorderBrush" Value="{StaticResource PrimaryDarkBrush}"/>
                <Setter Property="Foreground" Value="{StaticResource PrimaryDarkBrush}"/>
            </Trigger>

            <Trigger Property="IsEnabled" Value="False">
                <Setter Property="Background" Value="Transparent"/>
                <Setter Property="Foreground" Value="{StaticResource TextDisabledBrush}"/>
                <Setter Property="BorderBrush" Value="{StaticResource BorderSubtleBrush}"/>
            </Trigger>
        </Style.Triggers>
    </Style>

</ResourceDictionary>
