﻿using System.Windows;
using AirMonitor.ViewModels;

namespace AirMonitor;

/// <summary>
/// Interaction logic for MainWindow.xaml
/// </summary>
public partial class MainWindow : Window
{
    /// <summary>
    /// 构造函数，支持依赖注入
    /// </summary>
    /// <param name="viewModel">主窗口ViewModel</param>
    public MainWindow(MainWindowViewModel viewModel)
    {
        InitializeComponent();
        DataContext = viewModel;
    }
}