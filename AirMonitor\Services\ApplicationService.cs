using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using AirMonitor.Models;

namespace AirMonitor.Services;

/// <summary>
/// 应用程序服务实现
/// </summary>
public class ApplicationService : IApplicationService
{
    private readonly ILogger<ApplicationService> _logger;
    private readonly AppSettings _appSettings;

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="logger">日志服务</param>
    /// <param name="appSettings">应用程序设置</param>
    public ApplicationService(ILogger<ApplicationService> logger, IOptions<AppSettings> appSettings)
    {
        _logger = logger;
        _appSettings = appSettings.Value;
    }

    /// <summary>
    /// 初始化应用程序
    /// </summary>
    /// <returns></returns>
    public async Task InitializeAsync()
    {
        _logger.LogInformation("正在初始化应用程序...");
        
        // 这里可以添加应用程序初始化逻辑
        await Task.Delay(100); // 模拟异步操作
        
        _logger.LogInformation("应用程序初始化完成");
    }

    /// <summary>
    /// 获取应用程序信息
    /// </summary>
    /// <returns></returns>
    public string GetApplicationInfo()
    {
        return $"{_appSettings.Application.Name} v{_appSettings.Application.Version} ({_appSettings.Application.Environment})";
    }
}
