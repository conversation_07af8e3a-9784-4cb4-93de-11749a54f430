<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:sys="clr-namespace:System;assembly=mscorlib">

    <!-- ========================================
         FLUENT DESIGN BORDER RADIUS SYSTEM
         ======================================== -->

    <!-- ========================================
         CORNER RADIUS VALUES
         ======================================== -->

    <!-- None -->
    <CornerRadius x:Key="CornerRadiusNone">0</CornerRadius>

    <!-- Small Radius -->
    <CornerRadius x:Key="CornerRadiusXS">2</CornerRadius>
    <CornerRadius x:Key="CornerRadiusS">4</CornerRadius>

    <!-- Medium Radius -->
    <CornerRadius x:Key="CornerRadiusM">6</CornerRadius>
    <CornerRadius x:Key="CornerRadiusML">8</CornerRadius>

    <!-- Large Radius -->
    <CornerRadius x:Key="CornerRadiusL">12</CornerRadius>
    <CornerRadius x:Key="CornerRadiusXL">16</CornerRadius>

    <!-- Extra Large Radius -->
    <CornerRadius x:Key="CornerRadiusXXL">20</CornerRadius>
    <CornerRadius x:Key="CornerRadiusHuge">24</CornerRadius>

    <!-- Circular -->
    <CornerRadius x:Key="CornerRadiusCircular">9999</CornerRadius>

    <!-- ========================================
         COMPONENT-SPECIFIC CORNER RADIUS
         ======================================== -->

    <!-- Button Corner Radius -->
    <CornerRadius x:Key="ButtonCornerRadius">4</CornerRadius>
    <CornerRadius x:Key="ButtonCornerRadiusSmall">3</CornerRadius>
    <CornerRadius x:Key="ButtonCornerRadiusLarge">6</CornerRadius>
    <CornerRadius x:Key="ButtonCornerRadiusRounded">20</CornerRadius>

    <!-- Input Control Corner Radius -->
    <CornerRadius x:Key="InputCornerRadius">4</CornerRadius>
    <CornerRadius x:Key="InputCornerRadiusSmall">3</CornerRadius>
    <CornerRadius x:Key="InputCornerRadiusLarge">6</CornerRadius>

    <!-- Card Corner Radius -->
    <CornerRadius x:Key="CardCornerRadius">8</CornerRadius>
    <CornerRadius x:Key="CardCornerRadiusSmall">6</CornerRadius>
    <CornerRadius x:Key="CardCornerRadiusLarge">12</CornerRadius>

    <!-- Dialog Corner Radius -->
    <CornerRadius x:Key="DialogCornerRadius">8</CornerRadius>
    <CornerRadius x:Key="DialogCornerRadiusLarge">12</CornerRadius>

    <!-- Popup Corner Radius -->
    <CornerRadius x:Key="PopupCornerRadius">6</CornerRadius>
    <CornerRadius x:Key="TooltipCornerRadius">4</CornerRadius>

    <!-- Navigation Corner Radius -->
    <CornerRadius x:Key="NavigationItemCornerRadius">4</CornerRadius>
    <CornerRadius x:Key="NavigationPanelCornerRadius">8</CornerRadius>

    <!-- Badge Corner Radius -->
    <CornerRadius x:Key="BadgeCornerRadius">12</CornerRadius>
    <CornerRadius x:Key="BadgeCornerRadiusSmall">8</CornerRadius>

    <!-- Avatar Corner Radius -->
    <CornerRadius x:Key="AvatarCornerRadius">4</CornerRadius>
    <CornerRadius x:Key="AvatarCornerRadiusCircular">9999</CornerRadius>

    <!-- ========================================
         DOUBLE VALUES FOR RADIUS
         ======================================== -->

    <!-- Double values for use in code-behind or converters -->
    <sys:Double x:Key="CornerRadiusNoneValue">0</sys:Double>
    <sys:Double x:Key="CornerRadiusXSValue">2</sys:Double>
    <sys:Double x:Key="CornerRadiusSValue">4</sys:Double>
    <sys:Double x:Key="CornerRadiusMValue">6</sys:Double>
    <sys:Double x:Key="CornerRadiusMLValue">8</sys:Double>
    <sys:Double x:Key="CornerRadiusLValue">12</sys:Double>
    <sys:Double x:Key="CornerRadiusXLValue">16</sys:Double>
    <sys:Double x:Key="CornerRadiusXXLValue">20</sys:Double>
    <sys:Double x:Key="CornerRadiusHugeValue">24</sys:Double>

    <!-- ========================================
         SPECIALIZED CORNER RADIUS
         ======================================== -->

    <!-- Top Only Corner Radius -->
    <CornerRadius x:Key="CornerRadiusTopS">4,4,0,0</CornerRadius>
    <CornerRadius x:Key="CornerRadiusTopM">6,6,0,0</CornerRadius>
    <CornerRadius x:Key="CornerRadiusTopL">12,12,0,0</CornerRadius>

    <!-- Bottom Only Corner Radius -->
    <CornerRadius x:Key="CornerRadiusBottomS">0,0,4,4</CornerRadius>
    <CornerRadius x:Key="CornerRadiusBottomM">0,0,6,6</CornerRadius>
    <CornerRadius x:Key="CornerRadiusBottomL">0,0,12,12</CornerRadius>

    <!-- Left Only Corner Radius -->
    <CornerRadius x:Key="CornerRadiusLeftS">4,0,0,4</CornerRadius>
    <CornerRadius x:Key="CornerRadiusLeftM">6,0,0,6</CornerRadius>
    <CornerRadius x:Key="CornerRadiusLeftL">12,0,0,12</CornerRadius>

    <!-- Right Only Corner Radius -->
    <CornerRadius x:Key="CornerRadiusRightS">0,4,4,0</CornerRadius>
    <CornerRadius x:Key="CornerRadiusRightM">0,6,6,0</CornerRadius>
    <CornerRadius x:Key="CornerRadiusRightL">0,12,12,0</CornerRadius>

    <!-- ========================================
         BORDER THICKNESS VALUES
         ======================================== -->

    <!-- Standard Border Thickness -->
    <Thickness x:Key="BorderThicknessNone">0</Thickness>
    <Thickness x:Key="BorderThicknessThin">1</Thickness>
    <Thickness x:Key="BorderThicknessMedium">2</Thickness>
    <Thickness x:Key="BorderThicknessThick">3</Thickness>

    <!-- Component-Specific Border Thickness -->
    <Thickness x:Key="ButtonBorderThickness">1</Thickness>
    <Thickness x:Key="InputBorderThickness">1</Thickness>
    <Thickness x:Key="CardBorderThickness">1</Thickness>
    <Thickness x:Key="DialogBorderThickness">0</Thickness>
    <Thickness x:Key="PopupBorderThickness">1</Thickness>

    <!-- Focus Border Thickness -->
    <Thickness x:Key="FocusBorderThickness">2</Thickness>
    <Thickness x:Key="FocusBorderThicknessThick">3</Thickness>

    <!-- ========================================
         BORDER STYLES
         ======================================== -->

    <!-- Standard Border Style -->
    <Style x:Key="StandardBorderStyle" TargetType="Border">
        <Setter Property="BorderThickness" Value="{StaticResource BorderThicknessThin}"/>
        <Setter Property="BorderBrush" Value="{StaticResource BorderBrush}"/>
        <Setter Property="CornerRadius" Value="{StaticResource CornerRadiusM}"/>
    </Style>

    <!-- Card Border Style -->
    <Style x:Key="CardBorderStyle" TargetType="Border">
        <Setter Property="BorderThickness" Value="{StaticResource CardBorderThickness}"/>
        <Setter Property="BorderBrush" Value="{StaticResource BorderSubtleBrush}"/>
        <Setter Property="CornerRadius" Value="{StaticResource CardCornerRadius}"/>
        <Setter Property="Background" Value="{StaticResource SurfaceBrush}"/>
    </Style>

    <!-- Button Border Style -->
    <Style x:Key="ButtonBorderStyle" TargetType="Border">
        <Setter Property="BorderThickness" Value="{StaticResource ButtonBorderThickness}"/>
        <Setter Property="BorderBrush" Value="{StaticResource BorderBrush}"/>
        <Setter Property="CornerRadius" Value="{StaticResource ButtonCornerRadius}"/>
    </Style>

    <!-- Input Border Style -->
    <Style x:Key="InputBorderStyle" TargetType="Border">
        <Setter Property="BorderThickness" Value="{StaticResource InputBorderThickness}"/>
        <Setter Property="BorderBrush" Value="{StaticResource BorderBrush}"/>
        <Setter Property="CornerRadius" Value="{StaticResource InputCornerRadius}"/>
        <Setter Property="Background" Value="{StaticResource SurfaceBrush}"/>
    </Style>

    <!-- Popup Border Style -->
    <Style x:Key="PopupBorderStyle" TargetType="Border">
        <Setter Property="BorderThickness" Value="{StaticResource PopupBorderThickness}"/>
        <Setter Property="BorderBrush" Value="{StaticResource BorderBrush}"/>
        <Setter Property="CornerRadius" Value="{StaticResource PopupCornerRadius}"/>
        <Setter Property="Background" Value="{StaticResource SurfaceBrush}"/>
        <Setter Property="Effect" Value="{StaticResource PopupShadow}"/>
    </Style>

    <!-- ========================================
         FOCUS VISUAL STYLES
         ======================================== -->

    <!-- Focus Visual Style -->
    <Style x:Key="FocusVisualStyle">
        <Setter Property="Control.Template">
            <Setter.Value>
                <ControlTemplate>
                    <Border BorderThickness="{StaticResource FocusBorderThickness}"
                            BorderBrush="{StaticResource PrimaryBrush}"
                            CornerRadius="{StaticResource CornerRadiusM}"
                            Opacity="0.8"/>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- Keyboard Focus Visual Style -->
    <Style x:Key="KeyboardFocusVisualStyle">
        <Setter Property="Control.Template">
            <Setter.Value>
                <ControlTemplate>
                    <Border BorderThickness="{StaticResource FocusBorderThickness}"
                            BorderBrush="{StaticResource PrimaryBrush}"
                            CornerRadius="{StaticResource CornerRadiusM}">
                        <Border.Effect>
                            <DropShadowEffect Color="#0078D4" 
                                              Direction="0" 
                                              ShadowDepth="0" 
                                              BlurRadius="4" 
                                              Opacity="0.6"/>
                        </Border.Effect>
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

</ResourceDictionary>
