using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using AirMonitor.Models;
using AirMonitor.Services.Themes;

namespace AirMonitor.ViewModels;

/// <summary>
/// 主窗口ViewModel
/// </summary>
public partial class MainWindowViewModel : ViewModelBase
{
    private readonly ILogger<MainWindowViewModel> _logger;
    private readonly AppSettings _appSettings;
    private readonly IThemeManager _themeManager;

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="logger">日志服务</param>
    /// <param name="appSettings">应用程序设置</param>
    /// <param name="themeManager">主题管理器</param>
    public MainWindowViewModel(ILogger<MainWindowViewModel> logger, IOptions<AppSettings> appSettings, IThemeManager themeManager)
    {
        _logger = logger;
        _appSettings = appSettings.Value;
        _themeManager = themeManager;

        Title = _appSettings.Application.Name;
        StatusMessage = "应用程序已启动";

        // 监听主题变化
        _themeManager.ThemeChanged += OnThemeChanged;

        _logger.LogInformation("MainWindowViewModel 已初始化");
    }

    /// <summary>
    /// 示例命令
    /// </summary>
    [RelayCommand]
    private void ShowMessage()
    {
        _logger.LogInformation("ShowMessage 命令被执行");
        StatusMessage = $"当前时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss}";
    }

    /// <summary>
    /// 退出应用程序命令
    /// </summary>
    [RelayCommand]
    private void ExitApplication()
    {
        _logger.LogInformation("ExitApplication 命令被执行");
        System.Windows.Application.Current.Shutdown();
    }

    /// <summary>
    /// 切换主题命令
    /// </summary>
    [RelayCommand]
    private void ToggleTheme()
    {
        _logger.LogInformation("ToggleTheme 命令被执行");
        _themeManager.ToggleTheme();
    }

    /// <summary>
    /// 应用亮色主题命令
    /// </summary>
    [RelayCommand]
    private void ApplyLightTheme()
    {
        _logger.LogInformation("ApplyLightTheme 命令被执行");
        _themeManager.ApplyTheme(ThemeType.Light);
    }

    /// <summary>
    /// 应用暗色主题命令
    /// </summary>
    [RelayCommand]
    private void ApplyDarkTheme()
    {
        _logger.LogInformation("ApplyDarkTheme 命令被执行");
        _themeManager.ApplyTheme(ThemeType.Dark);
    }

    /// <summary>
    /// 应用系统主题命令
    /// </summary>
    [RelayCommand]
    private void ApplySystemTheme()
    {
        _logger.LogInformation("ApplySystemTheme 命令被执行");
        _themeManager.ApplyTheme(ThemeType.System);
    }

    /// <summary>
    /// 主题变化事件处理
    /// </summary>
    private void OnThemeChanged(object? sender, ThemeChangedEventArgs e)
    {
        StatusMessage = $"主题已切换到: {e.NewTheme} (实际: {e.ActualTheme})";
        _logger.LogInformation("主题已切换: {PreviousTheme} -> {NewTheme} (实际: {ActualTheme})",
            e.PreviousTheme, e.NewTheme, e.ActualTheme);
    }
}
