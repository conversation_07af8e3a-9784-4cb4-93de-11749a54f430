<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <!-- ========================================
         FLUENT DESIGN COLOR SYSTEM (DARK THEME)
         ======================================== -->

    <!-- Primary Colors (Same as Light Theme) -->
    <Color x:Key="PrimaryColor">#0078D4</Color>
    <Color x:Key="PrimaryLightColor">#40E0FF</Color>
    <Color x:Key="PrimaryDarkColor">#005A9E</Color>

    <!-- Secondary Colors (Same as Light Theme) -->
    <Color x:Key="SecondaryColor">#6B73FF</Color>
    <Color x:Key="SecondaryLightColor">#9AA0FF</Color>
    <Color x:Key="SecondaryDarkColor">#3D47CC</Color>

    <!-- Accent Colors (Same as Light Theme) -->
    <Color x:Key="AccentColor">#FF6B35</Color>
    <Color x:Key="AccentLightColor">#FF9A68</Color>
    <Color x:Key="AccentDarkColor">#CC3D00</Color>

    <!-- Semantic Colors (Adjusted for Dark Theme) -->
    <Color x:Key="SuccessColor">#54B054</Color>
    <Color x:Key="SuccessLightColor">#6FC06F</Color>
    <Color x:Key="SuccessDarkColor">#107C10</Color>
    
    <Color x:Key="WarningColor">#FFC83D</Color>
    <Color x:Key="WarningLightColor">#FFD666</Color>
    <Color x:Key="WarningDarkColor">#FFB900</Color>
    
    <Color x:Key="ErrorColor">#E74856</Color>
    <Color x:Key="ErrorLightColor">#F1707A</Color>
    <Color x:Key="ErrorDarkColor">#D13438</Color>
    
    <Color x:Key="InfoColor">#40E0FF</Color>
    <Color x:Key="InfoLightColor">#66E6FF</Color>
    <Color x:Key="InfoDarkColor">#0078D4</Color>

    <!-- Neutral Colors (Dark Theme) -->
    <Color x:Key="NeutralWhite">#000000</Color>
    <Color x:Key="Neutral50">#0F0F0F</Color>
    <Color x:Key="Neutral100">#1A1A1A</Color>
    <Color x:Key="Neutral200">#2D2D2D</Color>
    <Color x:Key="Neutral300">#404040</Color>
    <Color x:Key="Neutral400">#5C5C5C</Color>
    <Color x:Key="Neutral500">#757575</Color>
    <Color x:Key="Neutral600">#9E9E9E</Color>
    <Color x:Key="Neutral700">#BDBDBD</Color>
    <Color x:Key="Neutral800">#E0E0E0</Color>
    <Color x:Key="Neutral900">#F5F5F5</Color>
    <Color x:Key="NeutralBlack">#FFFFFF</Color>

    <!-- Surface Colors (Dark Theme) -->
    <Color x:Key="SurfaceColor">#1A1A1A</Color>
    <Color x:Key="SurfaceVariantColor">#2D2D2D</Color>
    <Color x:Key="BackgroundColor">#0F0F0F</Color>
    <Color x:Key="BackgroundSecondaryColor">#1A1A1A</Color>

    <!-- Text Colors (Dark Theme) -->
    <Color x:Key="TextPrimaryColor">#FFFFFF</Color>
    <Color x:Key="TextSecondaryColor">#E0E0E0</Color>
    <Color x:Key="TextTertiaryColor">#BDBDBD</Color>
    <Color x:Key="TextDisabledColor">#5C5C5C</Color>
    <Color x:Key="TextOnPrimaryColor">#FFFFFF</Color>
    <Color x:Key="TextOnSecondaryColor">#FFFFFF</Color>
    <Color x:Key="TextOnAccentColor">#FFFFFF</Color>

    <!-- Border Colors (Dark Theme) -->
    <Color x:Key="BorderColor">#404040</Color>
    <Color x:Key="BorderStrongColor">#757575</Color>
    <Color x:Key="BorderSubtleColor">#2D2D2D</Color>

    <!-- Divider Colors (Dark Theme) -->
    <Color x:Key="DividerColor">#404040</Color>
    <Color x:Key="DividerStrongColor">#5C5C5C</Color>

    <!-- Overlay Colors (Dark Theme) -->
    <Color x:Key="OverlayColor">#80000000</Color>
    <Color x:Key="OverlayLightColor">#40000000</Color>
    <Color x:Key="OverlayStrongColor">#B3000000</Color>

    <!-- ========================================
         SOLID COLOR BRUSHES (DARK THEME)
         ======================================== -->

    <!-- Primary Brushes -->
    <SolidColorBrush x:Key="PrimaryBrush" Color="{StaticResource PrimaryColor}"/>
    <SolidColorBrush x:Key="PrimaryLightBrush" Color="{StaticResource PrimaryLightColor}"/>
    <SolidColorBrush x:Key="PrimaryDarkBrush" Color="{StaticResource PrimaryDarkColor}"/>

    <!-- Secondary Brushes -->
    <SolidColorBrush x:Key="SecondaryBrush" Color="{StaticResource SecondaryColor}"/>
    <SolidColorBrush x:Key="SecondaryLightBrush" Color="{StaticResource SecondaryLightColor}"/>
    <SolidColorBrush x:Key="SecondaryDarkBrush" Color="{StaticResource SecondaryDarkColor}"/>

    <!-- Accent Brushes -->
    <SolidColorBrush x:Key="AccentBrush" Color="{StaticResource AccentColor}"/>
    <SolidColorBrush x:Key="AccentLightBrush" Color="{StaticResource AccentLightColor}"/>
    <SolidColorBrush x:Key="AccentDarkBrush" Color="{StaticResource AccentDarkColor}"/>

    <!-- Semantic Brushes -->
    <SolidColorBrush x:Key="SuccessBrush" Color="{StaticResource SuccessColor}"/>
    <SolidColorBrush x:Key="SuccessLightBrush" Color="{StaticResource SuccessLightColor}"/>
    <SolidColorBrush x:Key="SuccessDarkBrush" Color="{StaticResource SuccessDarkColor}"/>
    
    <SolidColorBrush x:Key="WarningBrush" Color="{StaticResource WarningColor}"/>
    <SolidColorBrush x:Key="WarningLightBrush" Color="{StaticResource WarningLightColor}"/>
    <SolidColorBrush x:Key="WarningDarkBrush" Color="{StaticResource WarningDarkColor}"/>
    
    <SolidColorBrush x:Key="ErrorBrush" Color="{StaticResource ErrorColor}"/>
    <SolidColorBrush x:Key="ErrorLightBrush" Color="{StaticResource ErrorLightColor}"/>
    <SolidColorBrush x:Key="ErrorDarkBrush" Color="{StaticResource ErrorDarkColor}"/>
    
    <SolidColorBrush x:Key="InfoBrush" Color="{StaticResource InfoColor}"/>
    <SolidColorBrush x:Key="InfoLightBrush" Color="{StaticResource InfoLightColor}"/>
    <SolidColorBrush x:Key="InfoDarkBrush" Color="{StaticResource InfoDarkColor}"/>

    <!-- Surface Brushes -->
    <SolidColorBrush x:Key="SurfaceBrush" Color="{StaticResource SurfaceColor}"/>
    <SolidColorBrush x:Key="SurfaceVariantBrush" Color="{StaticResource SurfaceVariantColor}"/>
    <SolidColorBrush x:Key="BackgroundBrush" Color="{StaticResource BackgroundColor}"/>
    <SolidColorBrush x:Key="BackgroundSecondaryBrush" Color="{StaticResource BackgroundSecondaryColor}"/>

    <!-- Text Brushes -->
    <SolidColorBrush x:Key="TextPrimaryBrush" Color="{StaticResource TextPrimaryColor}"/>
    <SolidColorBrush x:Key="TextSecondaryBrush" Color="{StaticResource TextSecondaryColor}"/>
    <SolidColorBrush x:Key="TextTertiaryBrush" Color="{StaticResource TextTertiaryColor}"/>
    <SolidColorBrush x:Key="TextDisabledBrush" Color="{StaticResource TextDisabledColor}"/>
    <SolidColorBrush x:Key="TextOnPrimaryBrush" Color="{StaticResource TextOnPrimaryColor}"/>
    <SolidColorBrush x:Key="TextOnSecondaryBrush" Color="{StaticResource TextOnSecondaryColor}"/>
    <SolidColorBrush x:Key="TextOnAccentBrush" Color="{StaticResource TextOnAccentColor}"/>

    <!-- Border Brushes -->
    <SolidColorBrush x:Key="BorderBrush" Color="{StaticResource BorderColor}"/>
    <SolidColorBrush x:Key="BorderStrongBrush" Color="{StaticResource BorderStrongColor}"/>
    <SolidColorBrush x:Key="BorderSubtleBrush" Color="{StaticResource BorderSubtleColor}"/>

    <!-- Divider Brushes -->
    <SolidColorBrush x:Key="DividerBrush" Color="{StaticResource DividerColor}"/>
    <SolidColorBrush x:Key="DividerStrongBrush" Color="{StaticResource DividerStrongColor}"/>

    <!-- Overlay Brushes -->
    <SolidColorBrush x:Key="OverlayBrush" Color="{StaticResource OverlayColor}"/>
    <SolidColorBrush x:Key="OverlayLightBrush" Color="{StaticResource OverlayLightColor}"/>
    <SolidColorBrush x:Key="OverlayStrongBrush" Color="{StaticResource OverlayStrongColor}"/>

    <!-- Neutral Brushes -->
    <SolidColorBrush x:Key="NeutralWhiteBrush" Color="{StaticResource NeutralWhite}"/>
    <SolidColorBrush x:Key="Neutral50Brush" Color="{StaticResource Neutral50}"/>
    <SolidColorBrush x:Key="Neutral100Brush" Color="{StaticResource Neutral100}"/>
    <SolidColorBrush x:Key="Neutral200Brush" Color="{StaticResource Neutral200}"/>
    <SolidColorBrush x:Key="Neutral300Brush" Color="{StaticResource Neutral300}"/>
    <SolidColorBrush x:Key="Neutral400Brush" Color="{StaticResource Neutral400}"/>
    <SolidColorBrush x:Key="Neutral500Brush" Color="{StaticResource Neutral500}"/>
    <SolidColorBrush x:Key="Neutral600Brush" Color="{StaticResource Neutral600}"/>
    <SolidColorBrush x:Key="Neutral700Brush" Color="{StaticResource Neutral700}"/>
    <SolidColorBrush x:Key="Neutral800Brush" Color="{StaticResource Neutral800}"/>
    <SolidColorBrush x:Key="Neutral900Brush" Color="{StaticResource Neutral900}"/>
    <SolidColorBrush x:Key="NeutralBlackBrush" Color="{StaticResource NeutralBlack}"/>

</ResourceDictionary>
