namespace AirMonitor.Models;

/// <summary>
/// 应用程序配置模型
/// </summary>
public class AppSettings
{
    /// <summary>
    /// 应用程序配置节
    /// </summary>
    public ApplicationSettings Application { get; set; } = new();
}

/// <summary>
/// 应用程序基本设置
/// </summary>
public class ApplicationSettings
{
    /// <summary>
    /// 应用程序名称
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 应用程序版本
    /// </summary>
    public string Version { get; set; } = string.Empty;

    /// <summary>
    /// 运行环境
    /// </summary>
    public string Environment { get; set; } = string.Empty;
}
