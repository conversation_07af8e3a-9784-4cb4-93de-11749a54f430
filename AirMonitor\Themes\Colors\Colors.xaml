<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <!-- ========================================
         FLUENT DESIGN COLOR SYSTEM
         ======================================== -->

    <!-- Primary Colors -->
    <Color x:Key="PrimaryColor">#0078D4</Color>
    <Color x:Key="PrimaryLightColor">#40E0FF</Color>
    <Color x:Key="PrimaryDarkColor">#005A9E</Color>
    
    <!-- Primary Color Variants -->
    <Color x:Key="Primary50">#E3F2FD</Color>
    <Color x:Key="Primary100">#BBDEFB</Color>
    <Color x:Key="Primary200">#90CAF9</Color>
    <Color x:Key="Primary300">#64B5F6</Color>
    <Color x:Key="Primary400">#42A5F5</Color>
    <Color x:Key="Primary500">#0078D4</Color>
    <Color x:Key="Primary600">#1E88E5</Color>
    <Color x:Key="Primary700">#1976D2</Color>
    <Color x:Key="Primary800">#1565C0</Color>
    <Color x:Key="Primary900">#0D47A1</Color>

    <!-- Secondary Colors -->
    <Color x:Key="SecondaryColor">#6B73FF</Color>
    <Color x:Key="SecondaryLightColor">#9AA0FF</Color>
    <Color x:Key="SecondaryDarkColor">#3D47CC</Color>
    
    <!-- Secondary Color Variants -->
    <Color x:Key="Secondary50">#E8EAF6</Color>
    <Color x:Key="Secondary100">#C5CAE9</Color>
    <Color x:Key="Secondary200">#9FA8DA</Color>
    <Color x:Key="Secondary300">#7986CB</Color>
    <Color x:Key="Secondary400">#5C6BC0</Color>
    <Color x:Key="Secondary500">#6B73FF</Color>
    <Color x:Key="Secondary600">#5E35B1</Color>
    <Color x:Key="Secondary700">#512DA8</Color>
    <Color x:Key="Secondary800">#4527A0</Color>
    <Color x:Key="Secondary900">#311B92</Color>

    <!-- Accent Colors -->
    <Color x:Key="AccentColor">#FF6B35</Color>
    <Color x:Key="AccentLightColor">#FF9A68</Color>
    <Color x:Key="AccentDarkColor">#CC3D00</Color>

    <!-- Semantic Colors -->
    <Color x:Key="SuccessColor">#107C10</Color>
    <Color x:Key="SuccessLightColor">#54B054</Color>
    <Color x:Key="SuccessDarkColor">#0B5A0B</Color>
    
    <Color x:Key="WarningColor">#FFB900</Color>
    <Color x:Key="WarningLightColor">#FFC83D</Color>
    <Color x:Key="WarningDarkColor">#CC9400</Color>
    
    <Color x:Key="ErrorColor">#D13438</Color>
    <Color x:Key="ErrorLightColor">#E74856</Color>
    <Color x:Key="ErrorDarkColor">#A4262C</Color>
    
    <Color x:Key="InfoColor">#0078D4</Color>
    <Color x:Key="InfoLightColor">#40E0FF</Color>
    <Color x:Key="InfoDarkColor">#005A9E</Color>

    <!-- Neutral Colors (Light Theme) -->
    <Color x:Key="NeutralWhite">#FFFFFF</Color>
    <Color x:Key="Neutral50">#FAFAFA</Color>
    <Color x:Key="Neutral100">#F5F5F5</Color>
    <Color x:Key="Neutral200">#EEEEEE</Color>
    <Color x:Key="Neutral300">#E0E0E0</Color>
    <Color x:Key="Neutral400">#BDBDBD</Color>
    <Color x:Key="Neutral500">#9E9E9E</Color>
    <Color x:Key="Neutral600">#757575</Color>
    <Color x:Key="Neutral700">#616161</Color>
    <Color x:Key="Neutral800">#424242</Color>
    <Color x:Key="Neutral900">#212121</Color>
    <Color x:Key="NeutralBlack">#000000</Color>

    <!-- Surface Colors (Light Theme) -->
    <Color x:Key="SurfaceColor">#FFFFFF</Color>
    <Color x:Key="SurfaceVariantColor">#F3F2F1</Color>
    <Color x:Key="BackgroundColor">#FAFAFA</Color>
    <Color x:Key="BackgroundSecondaryColor">#F5F5F5</Color>

    <!-- Text Colors (Light Theme) -->
    <Color x:Key="TextPrimaryColor">#323130</Color>
    <Color x:Key="TextSecondaryColor">#605E5C</Color>
    <Color x:Key="TextTertiaryColor">#8A8886</Color>
    <Color x:Key="TextDisabledColor">#C8C6C4</Color>
    <Color x:Key="TextOnPrimaryColor">#FFFFFF</Color>
    <Color x:Key="TextOnSecondaryColor">#FFFFFF</Color>
    <Color x:Key="TextOnAccentColor">#FFFFFF</Color>

    <!-- Border Colors (Light Theme) -->
    <Color x:Key="BorderColor">#E1DFDD</Color>
    <Color x:Key="BorderStrongColor">#8A8886</Color>
    <Color x:Key="BorderSubtleColor">#F3F2F1</Color>

    <!-- Divider Colors -->
    <Color x:Key="DividerColor">#E1DFDD</Color>
    <Color x:Key="DividerStrongColor">#C8C6C4</Color>

    <!-- Overlay Colors -->
    <Color x:Key="OverlayColor">#80000000</Color>
    <Color x:Key="OverlayLightColor">#40000000</Color>
    <Color x:Key="OverlayStrongColor">#B3000000</Color>

    <!-- ========================================
         SOLID COLOR BRUSHES (LIGHT THEME)
         ======================================== -->

    <!-- Primary Brushes -->
    <SolidColorBrush x:Key="PrimaryBrush" Color="{StaticResource PrimaryColor}"/>
    <SolidColorBrush x:Key="PrimaryLightBrush" Color="{StaticResource PrimaryLightColor}"/>
    <SolidColorBrush x:Key="PrimaryDarkBrush" Color="{StaticResource PrimaryDarkColor}"/>

    <!-- Secondary Brushes -->
    <SolidColorBrush x:Key="SecondaryBrush" Color="{StaticResource SecondaryColor}"/>
    <SolidColorBrush x:Key="SecondaryLightBrush" Color="{StaticResource SecondaryLightColor}"/>
    <SolidColorBrush x:Key="SecondaryDarkBrush" Color="{StaticResource SecondaryDarkColor}"/>

    <!-- Accent Brushes -->
    <SolidColorBrush x:Key="AccentBrush" Color="{StaticResource AccentColor}"/>
    <SolidColorBrush x:Key="AccentLightBrush" Color="{StaticResource AccentLightColor}"/>
    <SolidColorBrush x:Key="AccentDarkBrush" Color="{StaticResource AccentDarkColor}"/>

    <!-- Semantic Brushes -->
    <SolidColorBrush x:Key="SuccessBrush" Color="{StaticResource SuccessColor}"/>
    <SolidColorBrush x:Key="SuccessLightBrush" Color="{StaticResource SuccessLightColor}"/>
    <SolidColorBrush x:Key="SuccessDarkBrush" Color="{StaticResource SuccessDarkColor}"/>
    
    <SolidColorBrush x:Key="WarningBrush" Color="{StaticResource WarningColor}"/>
    <SolidColorBrush x:Key="WarningLightBrush" Color="{StaticResource WarningLightColor}"/>
    <SolidColorBrush x:Key="WarningDarkBrush" Color="{StaticResource WarningDarkColor}"/>
    
    <SolidColorBrush x:Key="ErrorBrush" Color="{StaticResource ErrorColor}"/>
    <SolidColorBrush x:Key="ErrorLightBrush" Color="{StaticResource ErrorLightColor}"/>
    <SolidColorBrush x:Key="ErrorDarkBrush" Color="{StaticResource ErrorDarkColor}"/>
    
    <SolidColorBrush x:Key="InfoBrush" Color="{StaticResource InfoColor}"/>
    <SolidColorBrush x:Key="InfoLightBrush" Color="{StaticResource InfoLightColor}"/>
    <SolidColorBrush x:Key="InfoDarkBrush" Color="{StaticResource InfoDarkColor}"/>

    <!-- Surface Brushes -->
    <SolidColorBrush x:Key="SurfaceBrush" Color="{StaticResource SurfaceColor}"/>
    <SolidColorBrush x:Key="SurfaceVariantBrush" Color="{StaticResource SurfaceVariantColor}"/>
    <SolidColorBrush x:Key="BackgroundBrush" Color="{StaticResource BackgroundColor}"/>
    <SolidColorBrush x:Key="BackgroundSecondaryBrush" Color="{StaticResource BackgroundSecondaryColor}"/>

    <!-- Text Brushes -->
    <SolidColorBrush x:Key="TextPrimaryBrush" Color="{StaticResource TextPrimaryColor}"/>
    <SolidColorBrush x:Key="TextSecondaryBrush" Color="{StaticResource TextSecondaryColor}"/>
    <SolidColorBrush x:Key="TextTertiaryBrush" Color="{StaticResource TextTertiaryColor}"/>
    <SolidColorBrush x:Key="TextDisabledBrush" Color="{StaticResource TextDisabledColor}"/>
    <SolidColorBrush x:Key="TextOnPrimaryBrush" Color="{StaticResource TextOnPrimaryColor}"/>
    <SolidColorBrush x:Key="TextOnSecondaryBrush" Color="{StaticResource TextOnSecondaryColor}"/>
    <SolidColorBrush x:Key="TextOnAccentBrush" Color="{StaticResource TextOnAccentColor}"/>

    <!-- Border Brushes -->
    <SolidColorBrush x:Key="BorderBrush" Color="{StaticResource BorderColor}"/>
    <SolidColorBrush x:Key="BorderStrongBrush" Color="{StaticResource BorderStrongColor}"/>
    <SolidColorBrush x:Key="BorderSubtleBrush" Color="{StaticResource BorderSubtleColor}"/>

    <!-- Divider Brushes -->
    <SolidColorBrush x:Key="DividerBrush" Color="{StaticResource DividerColor}"/>
    <SolidColorBrush x:Key="DividerStrongBrush" Color="{StaticResource DividerStrongColor}"/>

    <!-- Overlay Brushes -->
    <SolidColorBrush x:Key="OverlayBrush" Color="{StaticResource OverlayColor}"/>
    <SolidColorBrush x:Key="OverlayLightBrush" Color="{StaticResource OverlayLightColor}"/>
    <SolidColorBrush x:Key="OverlayStrongBrush" Color="{StaticResource OverlayStrongColor}"/>

    <!-- Neutral Brushes -->
    <SolidColorBrush x:Key="NeutralWhiteBrush" Color="{StaticResource NeutralWhite}"/>
    <SolidColorBrush x:Key="Neutral50Brush" Color="{StaticResource Neutral50}"/>
    <SolidColorBrush x:Key="Neutral100Brush" Color="{StaticResource Neutral100}"/>
    <SolidColorBrush x:Key="Neutral200Brush" Color="{StaticResource Neutral200}"/>
    <SolidColorBrush x:Key="Neutral300Brush" Color="{StaticResource Neutral300}"/>
    <SolidColorBrush x:Key="Neutral400Brush" Color="{StaticResource Neutral400}"/>
    <SolidColorBrush x:Key="Neutral500Brush" Color="{StaticResource Neutral500}"/>
    <SolidColorBrush x:Key="Neutral600Brush" Color="{StaticResource Neutral600}"/>
    <SolidColorBrush x:Key="Neutral700Brush" Color="{StaticResource Neutral700}"/>
    <SolidColorBrush x:Key="Neutral800Brush" Color="{StaticResource Neutral800}"/>
    <SolidColorBrush x:Key="Neutral900Brush" Color="{StaticResource Neutral900}"/>
    <SolidColorBrush x:Key="NeutralBlackBrush" Color="{StaticResource NeutralBlack}"/>

</ResourceDictionary>
