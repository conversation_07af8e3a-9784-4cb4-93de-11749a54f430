<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <!-- ========================================
         FLUENT DESIGN LIGHT THEME
         整合所有设计系统资源
         ======================================== -->

    <ResourceDictionary.MergedDictionaries>
        
        <!-- 基础设计系统资源 -->
        <ResourceDictionary Source="Spacing/Spacing.xaml"/>
        <ResourceDictionary Source="BorderRadius/BorderRadius.xaml"/>
        <ResourceDictionary Source="Typography/Typography.xaml"/>
        
        <!-- 亮色主题特定资源 -->
        <ResourceDictionary Source="Colors/Colors.xaml"/>
        <ResourceDictionary Source="Shadows/Shadows.xaml"/>
        
    </ResourceDictionary.MergedDictionaries>

    <!-- ========================================
         LIGHT THEME SPECIFIC OVERRIDES
         ======================================== -->

    <!-- 主题标识 -->
    <sys:String x:Key="ThemeName" xmlns:sys="clr-namespace:System;assembly=mscorlib">Light</sys:String>
    <sys:Boolean x:Key="IsDarkTheme" xmlns:sys="clr-namespace:System;assembly=mscorlib">False</sys:Boolean>

    <!-- 窗口背景 -->
    <SolidColorBrush x:Key="WindowBackgroundBrush" Color="{StaticResource BackgroundColor}"/>
    <SolidColorBrush x:Key="ApplicationBackgroundBrush" Color="{StaticResource BackgroundColor}"/>

    <!-- 控件默认样式覆盖 -->
    <SolidColorBrush x:Key="ControlBackgroundBrush" Color="{StaticResource SurfaceColor}"/>
    <SolidColorBrush x:Key="ControlForegroundBrush" Color="{StaticResource TextPrimaryColor}"/>
    <SolidColorBrush x:Key="ControlBorderBrush" Color="{StaticResource BorderColor}"/>

    <!-- 悬停状态 -->
    <SolidColorBrush x:Key="ControlHoverBackgroundBrush" Color="{StaticResource Neutral100}"/>
    <SolidColorBrush x:Key="ControlHoverBorderBrush" Color="{StaticResource BorderStrongColor}"/>

    <!-- 按下状态 -->
    <SolidColorBrush x:Key="ControlPressedBackgroundBrush" Color="{StaticResource Neutral200}"/>
    <SolidColorBrush x:Key="ControlPressedBorderBrush" Color="{StaticResource BorderStrongColor}"/>

    <!-- 禁用状态 -->
    <SolidColorBrush x:Key="ControlDisabledBackgroundBrush" Color="{StaticResource Neutral100}"/>
    <SolidColorBrush x:Key="ControlDisabledForegroundBrush" Color="{StaticResource TextDisabledColor}"/>
    <SolidColorBrush x:Key="ControlDisabledBorderBrush" Color="{StaticResource BorderSubtleColor}"/>

    <!-- 选中状态 -->
    <SolidColorBrush x:Key="ControlSelectedBackgroundBrush" Color="{StaticResource Primary100}"/>
    <SolidColorBrush x:Key="ControlSelectedForegroundBrush" Color="{StaticResource PrimaryColor}"/>
    <SolidColorBrush x:Key="ControlSelectedBorderBrush" Color="{StaticResource PrimaryColor}"/>

    <!-- 焦点状态 -->
    <SolidColorBrush x:Key="ControlFocusBackgroundBrush" Color="{StaticResource SurfaceColor}"/>
    <SolidColorBrush x:Key="ControlFocusBorderBrush" Color="{StaticResource PrimaryColor}"/>

    <!-- ========================================
         LIGHT THEME SEMANTIC COLORS
         ======================================== -->

    <!-- 状态指示器颜色 -->
    <SolidColorBrush x:Key="StatusSuccessBackgroundBrush" Color="{StaticResource SuccessColor}"/>
    <SolidColorBrush x:Key="StatusWarningBackgroundBrush" Color="{StaticResource WarningColor}"/>
    <SolidColorBrush x:Key="StatusErrorBackgroundBrush" Color="{StaticResource ErrorColor}"/>
    <SolidColorBrush x:Key="StatusInfoBackgroundBrush" Color="{StaticResource InfoColor}"/>

    <!-- 状态指示器文本颜色 -->
    <SolidColorBrush x:Key="StatusSuccessTextBrush" Color="{StaticResource NeutralWhite}"/>
    <SolidColorBrush x:Key="StatusWarningTextBrush" Color="{StaticResource NeutralBlack}"/>
    <SolidColorBrush x:Key="StatusErrorTextBrush" Color="{StaticResource NeutralWhite}"/>
    <SolidColorBrush x:Key="StatusInfoTextBrush" Color="{StaticResource NeutralWhite}"/>

    <!-- ========================================
         LIGHT THEME GRADIENTS
         ======================================== -->

    <!-- 主要渐变 -->
    <LinearGradientBrush x:Key="PrimaryGradientBrush" StartPoint="0,0" EndPoint="1,1">
        <GradientStop Color="{StaticResource PrimaryColor}" Offset="0"/>
        <GradientStop Color="{StaticResource PrimaryLightColor}" Offset="1"/>
    </LinearGradientBrush>

    <!-- 次要渐变 -->
    <LinearGradientBrush x:Key="SecondaryGradientBrush" StartPoint="0,0" EndPoint="1,1">
        <GradientStop Color="{StaticResource SecondaryColor}" Offset="0"/>
        <GradientStop Color="{StaticResource SecondaryLightColor}" Offset="1"/>
    </LinearGradientBrush>

    <!-- 强调渐变 -->
    <LinearGradientBrush x:Key="AccentGradientBrush" StartPoint="0,0" EndPoint="1,1">
        <GradientStop Color="{StaticResource AccentColor}" Offset="0"/>
        <GradientStop Color="{StaticResource AccentLightColor}" Offset="1"/>
    </LinearGradientBrush>

    <!-- 表面渐变 -->
    <LinearGradientBrush x:Key="SurfaceGradientBrush" StartPoint="0,0" EndPoint="0,1">
        <GradientStop Color="{StaticResource SurfaceColor}" Offset="0"/>
        <GradientStop Color="{StaticResource SurfaceVariantColor}" Offset="1"/>
    </LinearGradientBrush>

    <!-- ========================================
         LIGHT THEME ANIMATIONS
         ======================================== -->

    <!-- 淡入动画 -->
    <Storyboard x:Key="FadeInAnimation">
        <DoubleAnimation Storyboard.TargetProperty="Opacity"
                         From="0" To="1" Duration="0:0:0.3">
            <DoubleAnimation.EasingFunction>
                <CubicEase EasingMode="EaseOut"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
    </Storyboard>

    <!-- 淡出动画 -->
    <Storyboard x:Key="FadeOutAnimation">
        <DoubleAnimation Storyboard.TargetProperty="Opacity"
                         From="1" To="0" Duration="0:0:0.2">
            <DoubleAnimation.EasingFunction>
                <CubicEase EasingMode="EaseIn"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
    </Storyboard>

    <!-- 滑入动画 -->
    <Storyboard x:Key="SlideInAnimation">
        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(TranslateTransform.Y)"
                         From="20" To="0" Duration="0:0:0.3">
            <DoubleAnimation.EasingFunction>
                <CubicEase EasingMode="EaseOut"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
        <DoubleAnimation Storyboard.TargetProperty="Opacity"
                         From="0" To="1" Duration="0:0:0.3">
            <DoubleAnimation.EasingFunction>
                <CubicEase EasingMode="EaseOut"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
    </Storyboard>

    <!-- 缩放动画 -->
    <Storyboard x:Key="ScaleAnimation">
        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleX)"
                         From="0.95" To="1" Duration="0:0:0.2">
            <DoubleAnimation.EasingFunction>
                <BackEase EasingMode="EaseOut" Amplitude="0.3"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleY)"
                         From="0.95" To="1" Duration="0:0:0.2">
            <DoubleAnimation.EasingFunction>
                <BackEase EasingMode="EaseOut" Amplitude="0.3"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
    </Storyboard>

</ResourceDictionary>
