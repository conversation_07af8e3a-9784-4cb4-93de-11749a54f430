# AirMonitor Fluent Design 设计系统使用说明

## 🎉 问题已解决！

您的 WPF 项目现在已经成功集成了完整的 Fluent Design 设计系统，所有之前的错误都已修复：

### ✅ 已修复的问题：

1. **重复键定义错误** - 修复了 `Spacing.xaml` 中的重复键
2. **StaticResource 错误** - 将样式中的 StaticResource 改为 DynamicResource
3. **构造函数错误** - 移除了 App.xaml 中的 StartupUri，支持依赖注入

## 🚀 如何运行应用程序

```bash
cd AirMonitor
dotnet run
```

## 🎨 设计系统功能

### 1. 主题切换
应用程序现在支持三种主题：
- **亮色主题** - 现代明亮界面
- **暗色主题** - 护眼暗色界面  
- **系统主题** - 跟随系统设置

**使用方法：**
- 通过菜单栏 "主题" 选择不同主题
- 或使用 "切换主题" 快速切换

### 2. 色彩系统
- **主要颜色**：Primary (#0078D4), Secondary (#6B73FF), Accent (#FF6B35)
- **语义颜色**：Success (绿色), Warning (黄色), Error (红色), Info (蓝色)
- **中性颜色**：完整的灰度色阶
- **自动适配**：明暗主题自动调整颜色

### 3. 字体系统
- **Display 级别**：56px, 48px, 40px - 用于大标题
- **Headline 级别**：32px, 28px, 24px - 用于 H1-H3
- **Title 级别**：20px, 18px, 16px - 用于 H4-H6
- **Body 级别**：16px, 14px, 12px - 用于正文
- **Label 和 Caption**：用于标签和说明文字

### 4. 阴影系统
- **5个层级**：从轻微到深度阴影
- **专用阴影**：按钮、卡片、弹窗等
- **主题适配**：暗色主题阴影更深

### 5. 间距系统
- **8pt 网格**：基于 8 像素的间距系统
- **组件间距**：按钮、输入框、卡片等专用间距
- **响应式**：支持移动端和桌面端

### 6. 圆角系统
- **多级圆角**：从 2px 到 24px
- **组件圆角**：按钮、卡片、输入框等专用圆角
- **圆形支持**：头像等圆形元素

## 💡 在代码中使用设计系统

### XAML 中使用资源：

```xml
<!-- 使用颜色 -->
<Button Background="{DynamicResource PrimaryBrush}" 
        Foreground="{DynamicResource TextOnPrimaryBrush}"/>

<!-- 使用字体样式 -->
<TextBlock Text="标题" Style="{DynamicResource HeadlineLargeTextStyle}"/>

<!-- 使用间距 -->
<StackPanel Margin="{DynamicResource PageMargin}">
    <Button Padding="{DynamicResource ButtonPadding}"/>
</StackPanel>

<!-- 使用阴影 -->
<Border Effect="{DynamicResource CardShadow}"/>

<!-- 使用圆角 -->
<Border CornerRadius="{DynamicResource CardCornerRadius}"/>
```

### C# 中使用主题管理器：

```csharp
// 注入主题管理器
public MainWindow(IThemeManager themeManager)
{
    _themeManager = themeManager;
}

// 切换主题
_themeManager.ApplyTheme(ThemeType.Dark);
_themeManager.ToggleTheme();

// 监听主题变化
_themeManager.ThemeChanged += OnThemeChanged;
```

## 📁 文件结构

```
AirMonitor/
├── Themes/                    # 设计系统资源
│   ├── Colors/               # 色彩系统
│   │   ├── Colors.xaml       # 亮色主题
│   │   └── ColorsDark.xaml   # 暗色主题
│   ├── Typography/           # 字体系统
│   ├── Shadows/              # 阴影系统
│   ├── Spacing/              # 间距系统
│   ├── BorderRadius/         # 圆角系统
│   ├── Light.xaml            # 亮色主题整合
│   ├── Dark.xaml             # 暗色主题整合
│   └── README.md             # 详细文档
├── Services/Themes/          # 主题管理服务
│   ├── IThemeManager.cs      # 主题管理器接口
│   └── ThemeManager.cs       # 主题管理器实现
└── Views/
    └── DesignSystemDemo.xaml # 设计系统演示页面
```

## 🎯 最佳实践

### 1. 使用 DynamicResource
```xml
<!-- ✅ 推荐：支持主题切换 -->
<Button Background="{DynamicResource PrimaryBrush}"/>

<!-- ❌ 不推荐：不支持主题切换 -->
<Button Background="{StaticResource PrimaryBrush}"/>
```

### 2. 遵循命名约定
- 颜色：`PrimaryColor`, `SuccessColor`
- 画刷：`PrimaryBrush`, `SuccessBrush`
- 样式：`HeadlineLargeTextStyle`
- 间距：`ButtonPadding`, `CardMargin`

### 3. 使用语义化命名
```xml
<!-- ✅ 推荐：语义化 -->
<Border Background="{DynamicResource SuccessBrush}"/>

<!-- ❌ 不推荐：硬编码 -->
<Border Background="#107C10"/>
```

## 🔧 自定义扩展

### 添加新颜色：
在 `Colors.xaml` 和 `ColorsDark.xaml` 中添加：
```xml
<Color x:Key="CustomColor">#FF5722</Color>
<SolidColorBrush x:Key="CustomBrush" Color="{StaticResource CustomColor}"/>
```

### 添加新样式：
在 `Typography.xaml` 中添加：
```xml
<Style x:Key="CustomTextStyle" TargetType="TextBlock">
    <Setter Property="FontSize" Value="{DynamicResource FontSize18}"/>
    <Setter Property="FontWeight" Value="Medium"/>
    <Setter Property="Foreground" Value="{DynamicResource TextPrimaryBrush}"/>
</Style>
```

## 🐛 故障排除

### 如果主题不生效：
1. 确保使用 `DynamicResource` 而不是 `StaticResource`
2. 检查主题管理器是否正确注册和初始化
3. 验证资源键名是否正确

### 如果构建失败：
1. 检查是否有重复的资源键
2. 确保所有 XAML 文件语法正确
3. 验证依赖注入配置

## 📞 技术支持

如果遇到问题，请检查：
1. `Themes/README.md` - 详细的设计系统文档
2. 控制台日志 - 查看主题切换日志
3. Visual Studio 错误列表 - 查看编译错误

---

🎉 **恭喜！您的 WPF 应用现在拥有了完整的 Fluent Design 设计系统！**

享受现代化的用户界面和无缝的主题切换体验吧！
