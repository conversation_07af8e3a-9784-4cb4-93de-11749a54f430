# Fluent Design Button Styles Library

符合Microsoft Fluent Design System设计规范的完整WPF按钮样式库。

## 📋 目录

- [概述](#概述)
- [按钮类型](#按钮类型)
- [设计规范](#设计规范)
- [使用方法](#使用方法)
- [样式列表](#样式列表)
- [自定义指南](#自定义指南)
- [最佳实践](#最佳实践)

## 🎯 概述

本样式库提供了完整的按钮组件系统，包括：

- ✅ 6种主要按钮类型
- ✅ 多种尺寸变体
- ✅ 语义化颜色支持
- ✅ 完整的交互状态
- ✅ 流畅的动画效果
- ✅ 浅色/深色主题支持
- ✅ 无障碍访问支持

## 🔘 按钮类型

### 1. Primary Button (主要按钮)
用于页面中最重要的操作，具有最高的视觉优先级。

```xml
<Button Content="确认" Style="{StaticResource PrimaryButtonStyle}"/>
```

### 2. Secondary Button (次要按钮)
用于次要操作和取消操作。

```xml
<Button Content="取消" Style="{StaticResource SecondaryButtonStyle}"/>
```

### 3. Text Button (文本按钮)
用于低优先级操作，通常用作链接。

```xml
<Button Content="了解更多" Style="{StaticResource TextButtonStyle}"/>
```

### 4. Icon Button (图标按钮)
用于工具栏和紧凑布局，通常只包含图标。

```xml
<Button Content="⚙" Style="{StaticResource IconButtonStyle}"/>
```

### 5. Floating Action Button (浮动操作按钮)
用于页面的主要浮动操作，具有突出的视觉效果。

```xml
<Button Content="+" Style="{StaticResource FloatingActionButtonStyle}"/>
```

### 6. Toggle Button (切换按钮)
用于开关状态的切换操作。

```xml
<ToggleButton Content="开关" Style="{StaticResource ToggleButtonStyle}"/>
```

## 🎨 设计规范

### 圆角设计
- 标准按钮：4px 圆角
- 图标按钮：圆形
- 浮动按钮：圆形

### 阴影效果
- 正常状态：Elevation1Shadow (轻微阴影)
- 悬停状态：ButtonHoverShadow (中等阴影)
- 按下状态：ButtonPressedShadow (较浅阴影)
- 浮动按钮：Elevation3Shadow (明显阴影)

### 动画效果
- 悬停：轻微放大 (1.02倍)
- 按下：轻微缩小 (0.98倍)
- 焦点：焦点环淡入
- 持续时间：150-200ms
- 缓动函数：CubicEase

### 颜色系统
- 主要按钮：PrimaryBrush 背景
- 次要按钮：SurfaceBrush 背景 + BorderBrush 边框
- 文本按钮：透明背景 + PrimaryBrush 文字
- 浮动按钮：AccentBrush 背景

## 📖 使用方法

### 1. 引用样式文件

确保在主题文件中引用了按钮样式：

```xml
<ResourceDictionary.MergedDictionaries>
    <ResourceDictionary Source="../Styles/ButtonStyles.xaml"/>
</ResourceDictionary.MergedDictionaries>
```

### 2. 应用样式

```xml
<!-- 基本使用 -->
<Button Content="按钮文本" Style="{StaticResource PrimaryButtonStyle}"/>

<!-- 带图标 -->
<Button Style="{StaticResource IconButtonStyle}">
    <Path Data="M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2Z" 
          Fill="{Binding Foreground, RelativeSource={RelativeSource AncestorType=Button}}"/>
</Button>

<!-- 组合内容 -->
<Button Style="{StaticResource PrimaryButtonStyle}">
    <StackPanel Orientation="Horizontal" Spacing="8">
        <TextBlock Text="📁"/>
        <TextBlock Text="打开文件"/>
    </StackPanel>
</Button>
```

## 📝 样式列表

### 基础样式
- `PrimaryButtonStyle` - 主要按钮
- `SecondaryButtonStyle` - 次要按钮
- `TextButtonStyle` - 文本按钮
- `IconButtonStyle` - 图标按钮
- `FloatingActionButtonStyle` - 浮动操作按钮
- `ToggleButtonStyle` - 切换按钮

### 尺寸变体
- `SmallButtonStyle` - 小尺寸按钮 (基于PrimaryButtonStyle)
- `LargeButtonStyle` - 大尺寸按钮 (基于PrimaryButtonStyle)

### 语义化样式
- `SuccessButtonStyle` - 成功按钮 (绿色)
- `WarningButtonStyle` - 警告按钮 (黄色)
- `ErrorButtonStyle` - 错误按钮 (红色)

### 特殊样式
- `CircularButtonStyle` - 圆形按钮
- `OutlineButtonStyle` - 轮廓按钮

## 🛠 自定义指南

### 创建自定义按钮样式

```xml
<Style x:Key="CustomButtonStyle" TargetType="Button" BasedOn="{StaticResource PrimaryButtonStyle}">
    <Setter Property="Background" Value="#FF6B35"/>
    <Setter Property="BorderBrush" Value="#FF6B35"/>
    <Setter Property="Foreground" Value="White"/>
    
    <Style.Triggers>
        <Trigger Property="IsMouseOver" Value="True">
            <Setter Property="Background" Value="#E55A2B"/>
            <Setter Property="BorderBrush" Value="#E55A2B"/>
        </Trigger>
    </Style.Triggers>
</Style>
```

### 修改动画效果

```xml
<!-- 自定义悬停动画 -->
<Storyboard x:Key="CustomHoverAnimation">
    <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleX)"
                     To="1.05" Duration="0:0:0.2">
        <DoubleAnimation.EasingFunction>
            <BackEase EasingMode="EaseOut" Amplitude="0.3"/>
        </DoubleAnimation.EasingFunction>
    </DoubleAnimation>
</Storyboard>
```

## ✨ 最佳实践

### 1. 按钮层次结构
- 每个界面最多使用一个主要按钮
- 使用次要按钮作为替代操作
- 文本按钮用于最低优先级操作

### 2. 按钮组合
```xml
<!-- 对话框按钮组 -->
<StackPanel Orientation="Horizontal" HorizontalAlignment="Right" Spacing="8">
    <Button Content="取消" Style="{StaticResource SecondaryButtonStyle}"/>
    <Button Content="确认" Style="{StaticResource PrimaryButtonStyle}"/>
</StackPanel>
```

### 3. 工具栏按钮
```xml
<StackPanel Orientation="Horizontal" Spacing="4">
    <Button Content="📁" Style="{StaticResource IconButtonStyle}" ToolTip="打开"/>
    <Button Content="💾" Style="{StaticResource IconButtonStyle}" ToolTip="保存"/>
    <Separator Width="1" Height="24" Background="{StaticResource BorderBrush}"/>
    <Button Content="↶" Style="{StaticResource IconButtonStyle}" ToolTip="撤销"/>
</StackPanel>
```

### 4. 响应式设计
- 在小屏幕上优先使用图标按钮
- 考虑按钮的最小触摸目标 (44x44px)
- 为图标按钮提供工具提示

### 5. 无障碍访问
- 确保按钮有明确的文本或工具提示
- 支持键盘导航
- 提供足够的颜色对比度
- 使用语义化的按钮类型

## 🔧 技术细节

### 依赖资源
- Colors/Colors.xaml - 颜色系统
- Shadows/Shadows.xaml - 阴影效果
- BorderRadius/BorderRadius.xaml - 圆角系统
- Spacing/Spacing.xaml - 间距系统
- Typography/Typography.xaml - 字体系统

### 支持的控件
- `Button` - 标准按钮
- `ToggleButton` - 切换按钮
- 可扩展到其他按钮类型控件

### 浏览器兼容性
- 支持 .NET Framework 4.7.2+
- 支持 .NET Core 3.1+
- 支持 .NET 5/6/7+

---

💡 **提示**: 查看 `Views/ButtonStylesDemo.xaml` 获取完整的使用示例。
