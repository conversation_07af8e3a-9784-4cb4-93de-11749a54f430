﻿using System.IO;
using System.Windows;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Serilog;
using AirMonitor.Models;
using AirMonitor.Services;
using AirMonitor.Services.Themes;
using AirMonitor.ViewModels;

namespace AirMonitor;

/// <summary>
/// Interaction logic for App.xaml
/// </summary>
public partial class App : Application
{
    private IHost? _host;

    /// <summary>
    /// 应用程序启动时的配置
    /// </summary>
    /// <param name="e"></param>
    protected override async void OnStartup(StartupEventArgs e)
    {
        // 配置Serilog
        ConfigureSerilog();

        // 构建主机
        _host = CreateHostBuilder().Build();

        // 启动主机
        await _host.StartAsync();

        // 获取应用程序服务并初始化
        var appService = _host.Services.GetRequiredService<IApplicationService>();
        await appService.InitializeAsync();

        // 初始化主题管理器
        var themeManager = _host.Services.GetRequiredService<IThemeManager>();
        themeManager.ApplyTheme(ThemeType.System);

        // 创建并显示主窗口
        var mainWindow = _host.Services.GetRequiredService<MainWindow>();
        mainWindow.Show();

        base.OnStartup(e);
    }

    /// <summary>
    /// 应用程序退出时的清理
    /// </summary>
    /// <param name="e"></param>
    protected override async void OnExit(ExitEventArgs e)
    {
        if (_host != null)
        {
            await _host.StopAsync();
            _host.Dispose();
        }

        Log.CloseAndFlush();
        base.OnExit(e);
    }

    /// <summary>
    /// 创建主机构建器
    /// </summary>
    /// <returns></returns>
    private static IHostBuilder CreateHostBuilder()
    {
        return Host.CreateDefaultBuilder()
            .ConfigureAppConfiguration((context, config) =>
            {
                config.SetBasePath(Directory.GetCurrentDirectory());
                config.AddJsonFile("appsettings.json", optional: false, reloadOnChange: true);
            })
            .ConfigureServices((context, services) =>
            {
                // 配置应用程序设置
                services.Configure<AppSettings>(context.Configuration);

                // 注册服务
                services.AddSingleton<IApplicationService, ApplicationService>();
                services.AddSingleton<IThemeManager, ThemeManager>();

                // 注册ViewModels
                services.AddTransient<MainWindowViewModel>();

                // 注册Views
                services.AddTransient<MainWindow>();
            })
            .UseSerilog();
    }

    /// <summary>
    /// 配置Serilog日志
    /// </summary>
    private static void ConfigureSerilog()
    {
        var configuration = new ConfigurationBuilder()
            .SetBasePath(Directory.GetCurrentDirectory())
            .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
            .Build();

        Log.Logger = new LoggerConfiguration()
            .ReadFrom.Configuration(configuration)
            .CreateLogger();
    }
}

