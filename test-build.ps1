# AirMonitor 项目构建和测试脚本

Write-Host "=== AirMonitor 项目构建测试 ===" -ForegroundColor Green

# 1. 清理项目
Write-Host "1. 清理项目..." -ForegroundColor Yellow
dotnet clean

# 2. 还原包
Write-Host "2. 还原NuGet包..." -ForegroundColor Yellow
dotnet restore

# 3. 构建项目
Write-Host "3. 构建项目..." -ForegroundColor Yellow
$buildResult = dotnet build --configuration Release
if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ 构建成功!" -ForegroundColor Green
} else {
    Write-Host "❌ 构建失败!" -ForegroundColor Red
    exit 1
}

# 4. 检查输出文件
Write-Host "4. 检查输出文件..." -ForegroundColor Yellow
$outputPath = "AirMonitor\bin\Release\net9.0-windows\AirMonitor.exe"
if (Test-Path $outputPath) {
    Write-Host "✅ 可执行文件已生成: $outputPath" -ForegroundColor Green
} else {
    Write-Host "❌ 可执行文件未找到!" -ForegroundColor Red
}

# 5. 检查配置文件
$configPath = "AirMonitor\bin\Release\net9.0-windows\appsettings.json"
if (Test-Path $configPath) {
    Write-Host "✅ 配置文件已复制: $configPath" -ForegroundColor Green
} else {
    Write-Host "❌ 配置文件未找到!" -ForegroundColor Red
}

Write-Host "=== 测试完成 ===" -ForegroundColor Green
Write-Host "项目已准备就绪，可以运行: dotnet run --project AirMonitor/AirMonitor.csproj" -ForegroundColor Cyan
