# AirMonitor - .NET 9 WPF 应用程序框架

这是一个基于 .NET 9 的 WPF 应用程序框架，集成了现代化的开发模式和最佳实践。

## 🚀 功能特性

- ✅ **.NET 9** 框架支持
- ✅ **MVVM 架构** 使用 CommunityToolkit.Mvvm
- ✅ **依赖注入** 使用 Microsoft.Extensions.Hosting
- ✅ **配置管理** 支持 appsettings.json
- ✅ **日志系统** 集成 Serilog
- ✅ **源生成器** 自动生成属性和命令

## 📁 项目结构

```
AirMonitor/
├── Models/                 # 数据模型
│   └── AppSettings.cs     # 配置模型
├── Views/                 # 视图文件
├── ViewModels/            # 视图模型
│   ├── ViewModelBase.cs   # ViewModel基类
│   └── MainWindowViewModel.cs # 主窗口ViewModel
├── Services/              # 服务层
│   ├── IApplicationService.cs
│   └── ApplicationService.cs
├── App.xaml              # 应用程序入口
├── App.xaml.cs           # 应用程序配置
├── MainWindow.xaml       # 主窗口界面
├── MainWindow.xaml.cs    # 主窗口代码
└── appsettings.json      # 配置文件
```

## 🔧 技术栈

| 技术 | 版本 | 用途 |
|------|------|------|
| .NET | 9.0 | 运行时框架 |
| WPF | - | 用户界面框架 |
| CommunityToolkit.Mvvm | 8.4.0 | MVVM框架 |
| Serilog | 4.3.0 | 日志框架 |
| Microsoft.Extensions.Hosting | 9.0.6 | 依赖注入和配置 |

## 🛠️ 开发指南

### MVVM 模式使用

1. **ViewModel 继承**：所有 ViewModel 继承自 `ViewModelBase`
2. **属性绑定**：使用 `[ObservableProperty]` 特性自动生成属性
3. **命令绑定**：使用 `[RelayCommand]` 特性自动生成命令

```csharp
public partial class ExampleViewModel : ViewModelBase
{
    [ObservableProperty]
    private string _message = string.Empty;

    [RelayCommand]
    private void DoSomething()
    {
        Message = "Hello World!";
    }
}
```

### 依赖注入使用

在 `App.xaml.cs` 的 `ConfigureServices` 方法中注册服务：

```csharp
services.AddSingleton<IYourService, YourService>();
services.AddTransient<YourViewModel>();
```

### 配置管理

在 `appsettings.json` 中添加配置：

```json
{
  "Application": {
    "Name": "Your App Name",
    "Version": "1.0.0"
  }
}
```

在代码中使用：

```csharp
public class YourService
{
    public YourService(IOptions<AppSettings> settings)
    {
        var appName = settings.Value.Application.Name;
    }
}
```

### 日志使用

```csharp
public class YourService
{
    private readonly ILogger<YourService> _logger;

    public YourService(ILogger<YourService> logger)
    {
        _logger = logger;
    }

    public void DoWork()
    {
        _logger.LogInformation("开始执行工作");
    }
}
```

## 🚀 运行项目

1. 确保已安装 .NET 9 SDK
2. 克隆或下载项目
3. 在项目根目录执行：

```bash
dotnet restore
dotnet build
dotnet run --project AirMonitor/AirMonitor.csproj
```

## 📝 日志文件

日志文件将自动生成在 `logs/` 目录下，按日期滚动：
- 文件格式：`airmonitor-YYYY-MM-DD.log`
- 保留天数：30天

## 🔄 扩展开发

1. **添加新的 ViewModel**：
   - 在 `ViewModels` 文件夹创建新类
   - 继承 `ViewModelBase`
   - 在 `App.xaml.cs` 中注册

2. **添加新的服务**：
   - 在 `Services` 文件夹创建接口和实现
   - 在 `App.xaml.cs` 中注册

3. **添加新的配置**：
   - 在 `Models/AppSettings.cs` 中添加配置类
   - 在 `appsettings.json` 中添加配置值

## 📄 许可证

本项目仅作为开发框架模板使用。
