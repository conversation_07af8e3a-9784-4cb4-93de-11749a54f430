using System;

namespace AirMonitor.Services.Themes;

/// <summary>
/// 主题管理器接口
/// </summary>
public interface IThemeManager : IDisposable
{
    /// <summary>
    /// 当前主题类型
    /// </summary>
    ThemeType CurrentTheme { get; }

    /// <summary>
    /// 是否为暗色主题
    /// </summary>
    bool IsDarkTheme { get; }

    /// <summary>
    /// 主题变更事件
    /// </summary>
    event EventHandler<ThemeChangedEventArgs>? ThemeChanged;

    /// <summary>
    /// 应用主题
    /// </summary>
    /// <param name="theme">主题类型</param>
    void ApplyTheme(ThemeType theme);

    /// <summary>
    /// 切换主题
    /// </summary>
    void ToggleTheme();
}
