<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <!-- ========================================
         FLUENT DESIGN TYPOGRAPHY SYSTEM
         ======================================== -->

    <!-- Font Families -->
    <FontFamily x:Key="PrimaryFontFamily">Segoe UI</FontFamily>
    <FontFamily x:Key="SecondaryFontFamily">Segoe UI Variable</FontFamily>
    <FontFamily x:Key="MonospaceFontFamily">Consolas</FontFamily>
    <FontFamily x:Key="IconFontFamily">Segoe MDL2 Assets</FontFamily>

    <!-- Font Sizes -->
    <sys:Double x:Key="FontSize10" xmlns:sys="clr-namespace:System;assembly=mscorlib">10</sys:Double>
    <sys:Double x:Key="FontSize12" xmlns:sys="clr-namespace:System;assembly=mscorlib">12</sys:Double>
    <sys:Double x:Key="FontSize14" xmlns:sys="clr-namespace:System;assembly=mscorlib">14</sys:Double>
    <sys:Double x:Key="FontSize16" xmlns:sys="clr-namespace:System;assembly=mscorlib">16</sys:Double>
    <sys:Double x:Key="FontSize18" xmlns:sys="clr-namespace:System;assembly=mscorlib">18</sys:Double>
    <sys:Double x:Key="FontSize20" xmlns:sys="clr-namespace:System;assembly=mscorlib">20</sys:Double>
    <sys:Double x:Key="FontSize24" xmlns:sys="clr-namespace:System;assembly=mscorlib">24</sys:Double>
    <sys:Double x:Key="FontSize28" xmlns:sys="clr-namespace:System;assembly=mscorlib">28</sys:Double>
    <sys:Double x:Key="FontSize32" xmlns:sys="clr-namespace:System;assembly=mscorlib">32</sys:Double>
    <sys:Double x:Key="FontSize40" xmlns:sys="clr-namespace:System;assembly=mscorlib">40</sys:Double>
    <sys:Double x:Key="FontSize48" xmlns:sys="clr-namespace:System;assembly=mscorlib">48</sys:Double>
    <sys:Double x:Key="FontSize56" xmlns:sys="clr-namespace:System;assembly=mscorlib">56</sys:Double>

    <!-- Line Heights -->
    <sys:Double x:Key="LineHeight14" xmlns:sys="clr-namespace:System;assembly=mscorlib">14</sys:Double>
    <sys:Double x:Key="LineHeight16" xmlns:sys="clr-namespace:System;assembly=mscorlib">16</sys:Double>
    <sys:Double x:Key="LineHeight20" xmlns:sys="clr-namespace:System;assembly=mscorlib">20</sys:Double>
    <sys:Double x:Key="LineHeight22" xmlns:sys="clr-namespace:System;assembly=mscorlib">22</sys:Double>
    <sys:Double x:Key="LineHeight24" xmlns:sys="clr-namespace:System;assembly=mscorlib">24</sys:Double>
    <sys:Double x:Key="LineHeight28" xmlns:sys="clr-namespace:System;assembly=mscorlib">28</sys:Double>
    <sys:Double x:Key="LineHeight32" xmlns:sys="clr-namespace:System;assembly=mscorlib">32</sys:Double>
    <sys:Double x:Key="LineHeight36" xmlns:sys="clr-namespace:System;assembly=mscorlib">36</sys:Double>
    <sys:Double x:Key="LineHeight40" xmlns:sys="clr-namespace:System;assembly=mscorlib">40</sys:Double>
    <sys:Double x:Key="LineHeight52" xmlns:sys="clr-namespace:System;assembly=mscorlib">52</sys:Double>
    <sys:Double x:Key="LineHeight60" xmlns:sys="clr-namespace:System;assembly=mscorlib">60</sys:Double>
    <sys:Double x:Key="LineHeight68" xmlns:sys="clr-namespace:System;assembly=mscorlib">68</sys:Double>

    <!-- ========================================
         HEADING STYLES
         ======================================== -->

    <!-- Display Large -->
    <Style x:Key="DisplayLargeTextStyle" TargetType="TextBlock">
        <Setter Property="FontFamily" Value="{DynamicResource PrimaryFontFamily}"/>
        <Setter Property="FontSize" Value="{DynamicResource FontSize56}"/>
        <Setter Property="FontWeight" Value="SemiBold"/>
        <Setter Property="LineHeight" Value="{DynamicResource LineHeight68}"/>
        <Setter Property="Foreground" Value="{DynamicResource TextPrimaryBrush}"/>
    </Style>

    <!-- Display Medium -->
    <Style x:Key="DisplayMediumTextStyle" TargetType="TextBlock">
        <Setter Property="FontFamily" Value="{DynamicResource PrimaryFontFamily}"/>
        <Setter Property="FontSize" Value="{DynamicResource FontSize48}"/>
        <Setter Property="FontWeight" Value="SemiBold"/>
        <Setter Property="LineHeight" Value="{DynamicResource LineHeight60}"/>
        <Setter Property="Foreground" Value="{DynamicResource TextPrimaryBrush}"/>
    </Style>

    <!-- Display Small -->
    <Style x:Key="DisplaySmallTextStyle" TargetType="TextBlock">
        <Setter Property="FontFamily" Value="{DynamicResource PrimaryFontFamily}"/>
        <Setter Property="FontSize" Value="{DynamicResource FontSize40}"/>
        <Setter Property="FontWeight" Value="SemiBold"/>
        <Setter Property="LineHeight" Value="{DynamicResource LineHeight52}"/>
        <Setter Property="Foreground" Value="{DynamicResource TextPrimaryBrush}"/>
    </Style>

    <!-- Headline Large (H1) -->
    <Style x:Key="HeadlineLargeTextStyle" TargetType="TextBlock">
        <Setter Property="FontFamily" Value="{DynamicResource PrimaryFontFamily}"/>
        <Setter Property="FontSize" Value="{DynamicResource FontSize32}"/>
        <Setter Property="FontWeight" Value="SemiBold"/>
        <Setter Property="LineHeight" Value="{DynamicResource LineHeight40}"/>
        <Setter Property="Foreground" Value="{DynamicResource TextPrimaryBrush}"/>
    </Style>

    <!-- Headline Medium (H2) -->
    <Style x:Key="HeadlineMediumTextStyle" TargetType="TextBlock">
        <Setter Property="FontFamily" Value="{DynamicResource PrimaryFontFamily}"/>
        <Setter Property="FontSize" Value="{DynamicResource FontSize28}"/>
        <Setter Property="FontWeight" Value="SemiBold"/>
        <Setter Property="LineHeight" Value="{DynamicResource LineHeight36}"/>
        <Setter Property="Foreground" Value="{DynamicResource TextPrimaryBrush}"/>
    </Style>

    <!-- Headline Small (H3) -->
    <Style x:Key="HeadlineSmallTextStyle" TargetType="TextBlock">
        <Setter Property="FontFamily" Value="{DynamicResource PrimaryFontFamily}"/>
        <Setter Property="FontSize" Value="{DynamicResource FontSize24}"/>
        <Setter Property="FontWeight" Value="SemiBold"/>
        <Setter Property="LineHeight" Value="{DynamicResource LineHeight32}"/>
        <Setter Property="Foreground" Value="{DynamicResource TextPrimaryBrush}"/>
    </Style>

    <!-- Title Large (H4) -->
    <Style x:Key="TitleLargeTextStyle" TargetType="TextBlock">
        <Setter Property="FontFamily" Value="{DynamicResource PrimaryFontFamily}"/>
        <Setter Property="FontSize" Value="{DynamicResource FontSize20}"/>
        <Setter Property="FontWeight" Value="SemiBold"/>
        <Setter Property="LineHeight" Value="{DynamicResource LineHeight28}"/>
        <Setter Property="Foreground" Value="{DynamicResource TextPrimaryBrush}"/>
    </Style>

    <!-- Title Medium (H5) -->
    <Style x:Key="TitleMediumTextStyle" TargetType="TextBlock">
        <Setter Property="FontFamily" Value="{DynamicResource PrimaryFontFamily}"/>
        <Setter Property="FontSize" Value="{DynamicResource FontSize18}"/>
        <Setter Property="FontWeight" Value="SemiBold"/>
        <Setter Property="LineHeight" Value="{DynamicResource LineHeight24}"/>
        <Setter Property="Foreground" Value="{DynamicResource TextPrimaryBrush}"/>
    </Style>

    <!-- Title Small (H6) -->
    <Style x:Key="TitleSmallTextStyle" TargetType="TextBlock">
        <Setter Property="FontFamily" Value="{DynamicResource PrimaryFontFamily}"/>
        <Setter Property="FontSize" Value="{DynamicResource FontSize16}"/>
        <Setter Property="FontWeight" Value="SemiBold"/>
        <Setter Property="LineHeight" Value="{DynamicResource LineHeight22}"/>
        <Setter Property="Foreground" Value="{DynamicResource TextPrimaryBrush}"/>
    </Style>

    <!-- ========================================
         BODY TEXT STYLES
         ======================================== -->

    <!-- Body Large -->
    <Style x:Key="BodyLargeTextStyle" TargetType="TextBlock">
        <Setter Property="FontFamily" Value="{DynamicResource PrimaryFontFamily}"/>
        <Setter Property="FontSize" Value="{DynamicResource FontSize16}"/>
        <Setter Property="FontWeight" Value="Normal"/>
        <Setter Property="LineHeight" Value="{DynamicResource LineHeight22}"/>
        <Setter Property="Foreground" Value="{DynamicResource TextPrimaryBrush}"/>
    </Style>

    <!-- Body Medium -->
    <Style x:Key="BodyMediumTextStyle" TargetType="TextBlock">
        <Setter Property="FontFamily" Value="{DynamicResource PrimaryFontFamily}"/>
        <Setter Property="FontSize" Value="{DynamicResource FontSize14}"/>
        <Setter Property="FontWeight" Value="Normal"/>
        <Setter Property="LineHeight" Value="{DynamicResource LineHeight20}"/>
        <Setter Property="Foreground" Value="{DynamicResource TextPrimaryBrush}"/>
    </Style>

    <!-- Body Small -->
    <Style x:Key="BodySmallTextStyle" TargetType="TextBlock">
        <Setter Property="FontFamily" Value="{DynamicResource PrimaryFontFamily}"/>
        <Setter Property="FontSize" Value="{DynamicResource FontSize12}"/>
        <Setter Property="FontWeight" Value="Normal"/>
        <Setter Property="LineHeight" Value="{DynamicResource LineHeight16}"/>
        <Setter Property="Foreground" Value="{DynamicResource TextSecondaryBrush}"/>
    </Style>

    <!-- ========================================
         LABEL AND CAPTION STYLES
         ======================================== -->

    <!-- Label Large -->
    <Style x:Key="LabelLargeTextStyle" TargetType="TextBlock">
        <Setter Property="FontFamily" Value="{DynamicResource PrimaryFontFamily}"/>
        <Setter Property="FontSize" Value="{DynamicResource FontSize14}"/>
        <Setter Property="FontWeight" Value="Medium"/>
        <Setter Property="LineHeight" Value="{DynamicResource LineHeight20}"/>
        <Setter Property="Foreground" Value="{DynamicResource TextPrimaryBrush}"/>
    </Style>

    <!-- Label Medium -->
    <Style x:Key="LabelMediumTextStyle" TargetType="TextBlock">
        <Setter Property="FontFamily" Value="{DynamicResource PrimaryFontFamily}"/>
        <Setter Property="FontSize" Value="{DynamicResource FontSize12}"/>
        <Setter Property="FontWeight" Value="Medium"/>
        <Setter Property="LineHeight" Value="{DynamicResource LineHeight16}"/>
        <Setter Property="Foreground" Value="{DynamicResource TextPrimaryBrush}"/>
    </Style>

    <!-- Label Small -->
    <Style x:Key="LabelSmallTextStyle" TargetType="TextBlock">
        <Setter Property="FontFamily" Value="{DynamicResource PrimaryFontFamily}"/>
        <Setter Property="FontSize" Value="{DynamicResource FontSize10}"/>
        <Setter Property="FontWeight" Value="Medium"/>
        <Setter Property="LineHeight" Value="{DynamicResource LineHeight14}"/>
        <Setter Property="Foreground" Value="{DynamicResource TextSecondaryBrush}"/>
    </Style>

    <!-- Caption -->
    <Style x:Key="CaptionTextStyle" TargetType="TextBlock">
        <Setter Property="FontFamily" Value="{DynamicResource PrimaryFontFamily}"/>
        <Setter Property="FontSize" Value="{DynamicResource FontSize10}"/>
        <Setter Property="FontWeight" Value="Normal"/>
        <Setter Property="LineHeight" Value="{DynamicResource LineHeight14}"/>
        <Setter Property="Foreground" Value="{DynamicResource TextTertiaryBrush}"/>
    </Style>

    <!-- ========================================
         SPECIALIZED TEXT STYLES
         ======================================== -->

    <!-- Button Text -->
    <Style x:Key="ButtonTextStyle" TargetType="TextBlock">
        <Setter Property="FontFamily" Value="{DynamicResource PrimaryFontFamily}"/>
        <Setter Property="FontSize" Value="{DynamicResource FontSize14}"/>
        <Setter Property="FontWeight" Value="Medium"/>
        <Setter Property="LineHeight" Value="{DynamicResource LineHeight20}"/>
        <Setter Property="Foreground" Value="{DynamicResource TextOnPrimaryBrush}"/>
    </Style>

    <!-- Input Text -->
    <Style x:Key="InputTextStyle" TargetType="TextBox">
        <Setter Property="FontFamily" Value="{DynamicResource PrimaryFontFamily}"/>
        <Setter Property="FontSize" Value="{DynamicResource FontSize14}"/>
        <Setter Property="FontWeight" Value="Normal"/>
        <Setter Property="Foreground" Value="{DynamicResource TextPrimaryBrush}"/>
    </Style>

    <!-- Monospace Text -->
    <Style x:Key="MonospaceTextStyle" TargetType="TextBlock">
        <Setter Property="FontFamily" Value="{DynamicResource MonospaceFontFamily}"/>
        <Setter Property="FontSize" Value="{DynamicResource FontSize14}"/>
        <Setter Property="FontWeight" Value="Normal"/>
        <Setter Property="LineHeight" Value="{DynamicResource LineHeight20}"/>
        <Setter Property="Foreground" Value="{DynamicResource TextPrimaryBrush}"/>
    </Style>

    <!-- Link Text -->
    <Style x:Key="LinkTextStyle" TargetType="TextBlock">
        <Setter Property="FontFamily" Value="{DynamicResource PrimaryFontFamily}"/>
        <Setter Property="FontSize" Value="{DynamicResource FontSize14}"/>
        <Setter Property="FontWeight" Value="Normal"/>
        <Setter Property="LineHeight" Value="{DynamicResource LineHeight20}"/>
        <Setter Property="Foreground" Value="{DynamicResource PrimaryBrush}"/>
        <Setter Property="TextDecorations" Value="Underline"/>
        <Setter Property="Cursor" Value="Hand"/>
    </Style>

</ResourceDictionary>
