<UserControl x:Class="AirMonitor.Views.DesignSystemDemo"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             mc:Ignorable="d" 
             d:DesignHeight="800" d:DesignWidth="1200">
    
    <ScrollViewer VerticalScrollBarVisibility="Auto" HorizontalScrollBarVisibility="Disabled">
        <StackPanel Margin="{StaticResource PageMargin}">
            
            <!-- 标题区域 -->
            <StackPanel Margin="{StaticResource SectionMargin}">
                <TextBlock Text="Fluent Design 设计系统演示" 
                           Style="{StaticResource DisplayLargeTextStyle}"
                           Margin="{StaticResource HeaderMargin}"/>
                <TextBlock Text="展示设计系统中的各种组件和样式" 
                           Style="{StaticResource BodyLargeTextStyle}"
                           Foreground="{DynamicResource TextSecondaryBrush}"/>
            </StackPanel>
            
            <!-- 主题切换区域 -->
            <Border Background="{DynamicResource SurfaceBrush}"
                    BorderBrush="{DynamicResource BorderBrush}"
                    BorderThickness="{StaticResource BorderThicknessThin}"
                    CornerRadius="{StaticResource CardCornerRadius}"
                    Padding="{StaticResource CardPadding}"
                    Margin="{StaticResource SectionMargin}"
                    Effect="{StaticResource CardShadow}">
                
                <StackPanel>
                    <TextBlock Text="主题控制" 
                               Style="{StaticResource TitleLargeTextStyle}"
                               Margin="{StaticResource ContentMarginSmall}"/>
                    
                    <StackPanel Orientation="Horizontal" Margin="{StaticResource ContentMargin}">
                        <Button Content="亮色主题"
                                Padding="{StaticResource ButtonPadding}"
                                Margin="{StaticResource ThicknessS}"
                                Background="{DynamicResource PrimaryBrush}"
                                Foreground="{DynamicResource TextOnPrimaryBrush}"
                                BorderThickness="0"/>

                        <Button Content="暗色主题"
                                Padding="{StaticResource ButtonPadding}"
                                Margin="{StaticResource ThicknessS}"
                                Background="{DynamicResource SecondaryBrush}"
                                Foreground="{DynamicResource TextOnSecondaryBrush}"
                                BorderThickness="0"/>

                        <Button Content="系统主题"
                                Padding="{StaticResource ButtonPadding}"
                                Margin="{StaticResource ThicknessS}"
                                Background="{DynamicResource AccentBrush}"
                                Foreground="{DynamicResource TextOnAccentBrush}"
                                BorderThickness="0"/>
                    </StackPanel>
                </StackPanel>
            </Border>
            
            <!-- 色彩系统演示 -->
            <Border Background="{DynamicResource SurfaceBrush}"
                    BorderBrush="{DynamicResource BorderBrush}"
                    BorderThickness="{StaticResource BorderThicknessThin}"
                    CornerRadius="{StaticResource CardCornerRadius}"
                    Padding="{StaticResource CardPadding}"
                    Margin="{StaticResource SectionMargin}"
                    Effect="{StaticResource CardShadow}">
                
                <StackPanel>
                    <TextBlock Text="色彩系统" 
                               Style="{StaticResource TitleLargeTextStyle}"
                               Margin="{StaticResource ContentMarginSmall}"/>
                    
                    <!-- 主要颜色 -->
                    <TextBlock Text="主要颜色" 
                               Style="{StaticResource TitleMediumTextStyle}"
                               Margin="{StaticResource ContentMarginSmall}"/>
                    
                    <StackPanel Orientation="Horizontal" Margin="{StaticResource ContentMargin}">
                        <Border Width="80" Height="60" 
                                Background="{DynamicResource PrimaryBrush}"
                                CornerRadius="{StaticResource CornerRadiusS}"
                                Margin="{StaticResource ThicknessS}">
                            <TextBlock Text="Primary" 
                                       Foreground="{DynamicResource TextOnPrimaryBrush}"
                                       HorizontalAlignment="Center"
                                       VerticalAlignment="Center"
                                       Style="{StaticResource LabelMediumTextStyle}"/>
                        </Border>
                        
                        <Border Width="80" Height="60" 
                                Background="{DynamicResource SecondaryBrush}"
                                CornerRadius="{StaticResource CornerRadiusS}"
                                Margin="{StaticResource ThicknessS}">
                            <TextBlock Text="Secondary" 
                                       Foreground="{DynamicResource TextOnSecondaryBrush}"
                                       HorizontalAlignment="Center"
                                       VerticalAlignment="Center"
                                       Style="{StaticResource LabelMediumTextStyle}"/>
                        </Border>
                        
                        <Border Width="80" Height="60" 
                                Background="{DynamicResource AccentBrush}"
                                CornerRadius="{StaticResource CornerRadiusS}"
                                Margin="{StaticResource ThicknessS}">
                            <TextBlock Text="Accent" 
                                       Foreground="{DynamicResource TextOnAccentBrush}"
                                       HorizontalAlignment="Center"
                                       VerticalAlignment="Center"
                                       Style="{StaticResource LabelMediumTextStyle}"/>
                        </Border>
                    </StackPanel>
                    
                    <!-- 语义化颜色 -->
                    <TextBlock Text="语义化颜色" 
                               Style="{StaticResource TitleMediumTextStyle}"
                               Margin="{StaticResource ContentMarginSmall}"/>
                    
                    <StackPanel Orientation="Horizontal" Margin="{StaticResource ContentMargin}">
                        <Border Width="80" Height="60" 
                                Background="{DynamicResource SuccessBrush}"
                                CornerRadius="{StaticResource CornerRadiusS}"
                                Margin="{StaticResource ThicknessS}">
                            <TextBlock Text="Success" 
                                       Foreground="White"
                                       HorizontalAlignment="Center"
                                       VerticalAlignment="Center"
                                       Style="{StaticResource LabelMediumTextStyle}"/>
                        </Border>
                        
                        <Border Width="80" Height="60" 
                                Background="{DynamicResource WarningBrush}"
                                CornerRadius="{StaticResource CornerRadiusS}"
                                Margin="{StaticResource ThicknessS}">
                            <TextBlock Text="Warning" 
                                       Foreground="Black"
                                       HorizontalAlignment="Center"
                                       VerticalAlignment="Center"
                                       Style="{StaticResource LabelMediumTextStyle}"/>
                        </Border>
                        
                        <Border Width="80" Height="60" 
                                Background="{DynamicResource ErrorBrush}"
                                CornerRadius="{StaticResource CornerRadiusS}"
                                Margin="{StaticResource ThicknessS}">
                            <TextBlock Text="Error" 
                                       Foreground="White"
                                       HorizontalAlignment="Center"
                                       VerticalAlignment="Center"
                                       Style="{StaticResource LabelMediumTextStyle}"/>
                        </Border>
                        
                        <Border Width="80" Height="60" 
                                Background="{DynamicResource InfoBrush}"
                                CornerRadius="{StaticResource CornerRadiusS}"
                                Margin="{StaticResource ThicknessS}">
                            <TextBlock Text="Info" 
                                       Foreground="White"
                                       HorizontalAlignment="Center"
                                       VerticalAlignment="Center"
                                       Style="{StaticResource LabelMediumTextStyle}"/>
                        </Border>
                    </StackPanel>
                </StackPanel>
            </Border>
            
            <!-- 字体系统演示 -->
            <Border Background="{DynamicResource SurfaceBrush}"
                    BorderBrush="{DynamicResource BorderBrush}"
                    BorderThickness="{StaticResource BorderThicknessThin}"
                    CornerRadius="{StaticResource CardCornerRadius}"
                    Padding="{StaticResource CardPadding}"
                    Margin="{StaticResource SectionMargin}"
                    Effect="{StaticResource CardShadow}">
                
                <StackPanel>
                    <TextBlock Text="字体系统" 
                               Style="{StaticResource TitleLargeTextStyle}"
                               Margin="{StaticResource ContentMarginSmall}"/>
                    
                    <TextBlock Text="Display Large (56px)" Style="{StaticResource DisplayLargeTextStyle}" Margin="{StaticResource ContentMarginSmall}"/>
                    <TextBlock Text="Display Medium (48px)" Style="{StaticResource DisplayMediumTextStyle}" Margin="{StaticResource ContentMarginSmall}"/>
                    <TextBlock Text="Headline Large (32px)" Style="{StaticResource HeadlineLargeTextStyle}" Margin="{StaticResource ContentMarginSmall}"/>
                    <TextBlock Text="Headline Medium (28px)" Style="{StaticResource HeadlineMediumTextStyle}" Margin="{StaticResource ContentMarginSmall}"/>
                    <TextBlock Text="Title Large (20px)" Style="{StaticResource TitleLargeTextStyle}" Margin="{StaticResource ContentMarginSmall}"/>
                    <TextBlock Text="Title Medium (18px)" Style="{StaticResource TitleMediumTextStyle}" Margin="{StaticResource ContentMarginSmall}"/>
                    <TextBlock Text="Body Large (16px)" Style="{StaticResource BodyLargeTextStyle}" Margin="{StaticResource ContentMarginSmall}"/>
                    <TextBlock Text="Body Medium (14px)" Style="{StaticResource BodyMediumTextStyle}" Margin="{StaticResource ContentMarginSmall}"/>
                    <TextBlock Text="Body Small (12px)" Style="{StaticResource BodySmallTextStyle}" Margin="{StaticResource ContentMarginSmall}"/>
                    <TextBlock Text="Caption (10px)" Style="{StaticResource CaptionTextStyle}" Margin="{StaticResource ContentMarginSmall}"/>
                </StackPanel>
            </Border>
            
            <!-- 阴影系统演示 -->
            <Border Background="{DynamicResource SurfaceBrush}"
                    BorderBrush="{DynamicResource BorderBrush}"
                    BorderThickness="{StaticResource BorderThicknessThin}"
                    CornerRadius="{StaticResource CardCornerRadius}"
                    Padding="{StaticResource CardPadding}"
                    Margin="{StaticResource SectionMargin}"
                    Effect="{StaticResource CardShadow}">
                
                <StackPanel>
                    <TextBlock Text="阴影系统" 
                               Style="{StaticResource TitleLargeTextStyle}"
                               Margin="{StaticResource ContentMarginSmall}"/>
                    
                    <StackPanel Orientation="Horizontal" Margin="{StaticResource ContentMargin}">
                        <Border Width="100" Height="80" 
                                Background="{DynamicResource SurfaceBrush}"
                                CornerRadius="{StaticResource CornerRadiusM}"
                                Margin="{StaticResource ThicknessM}"
                                Effect="{StaticResource Elevation1Shadow}">
                            <TextBlock Text="Elevation 1" 
                                       HorizontalAlignment="Center"
                                       VerticalAlignment="Center"
                                       Style="{StaticResource LabelMediumTextStyle}"/>
                        </Border>
                        
                        <Border Width="100" Height="80" 
                                Background="{DynamicResource SurfaceBrush}"
                                CornerRadius="{StaticResource CornerRadiusM}"
                                Margin="{StaticResource ThicknessM}"
                                Effect="{StaticResource Elevation2Shadow}">
                            <TextBlock Text="Elevation 2" 
                                       HorizontalAlignment="Center"
                                       VerticalAlignment="Center"
                                       Style="{StaticResource LabelMediumTextStyle}"/>
                        </Border>
                        
                        <Border Width="100" Height="80" 
                                Background="{DynamicResource SurfaceBrush}"
                                CornerRadius="{StaticResource CornerRadiusM}"
                                Margin="{StaticResource ThicknessM}"
                                Effect="{StaticResource Elevation3Shadow}">
                            <TextBlock Text="Elevation 3" 
                                       HorizontalAlignment="Center"
                                       VerticalAlignment="Center"
                                       Style="{StaticResource LabelMediumTextStyle}"/>
                        </Border>
                    </StackPanel>
                </StackPanel>
            </Border>
            
        </StackPanel>
    </ScrollViewer>
</UserControl>
