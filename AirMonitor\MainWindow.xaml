<Window x:Class="AirMonitor.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:AirMonitor"
        xmlns:vm="clr-namespace:AirMonitor.ViewModels"
        mc:Ignorable="d"
        Title="{Binding Title}" Height="450" Width="800"
        WindowStartupLocation="CenterScreen">



    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 菜单栏 -->
        <Menu Grid.Row="0">
            <MenuItem Header="文件(_F)">
                <MenuItem Header="退出(_X)" Command="{Binding ExitApplicationCommand}"/>
            </MenuItem>
            <MenuItem Header="主题(_T)">
                <MenuItem Header="亮色主题(_L)" Command="{Binding ApplyLightThemeCommand}"/>
                <MenuItem Header="暗色主题(_D)" Command="{Binding ApplyDarkThemeCommand}"/>
                <MenuItem Header="系统主题(_S)" Command="{Binding ApplySystemThemeCommand}"/>
                <Separator/>
                <MenuItem Header="切换主题(_T)" Command="{Binding ToggleThemeCommand}"/>
            </MenuItem>
            <MenuItem Header="帮助(_H)">
                <MenuItem Header="关于(_A)" Command="{Binding ShowMessageCommand}"/>
            </MenuItem>
        </Menu>

        <!-- 主内容区域 -->
        <Grid Grid.Row="1" Margin="{DynamicResource PageMargin}">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <TextBlock Grid.Row="0"
                       Text="欢迎使用 AirMonitor 应用程序框架"
                       Style="{DynamicResource HeadlineLargeTextStyle}"
                       HorizontalAlignment="Center"
                       Margin="{DynamicResource HeaderMargin}"/>

            <StackPanel Grid.Row="1"
                        HorizontalAlignment="Center"
                        VerticalAlignment="Center">

                <!-- 按钮样式测试 -->
                <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,0,0,16">
                    <Button Content="Primary"
                            Style="{StaticResource PrimaryButtonStyle}"
                            Margin="0,0,8,0"/>
                    <Button Content="Secondary"
                            Style="{StaticResource SecondaryButtonStyle}"
                            Margin="0,0,8,0"/>
                    <Button Content="Text"
                            Style="{StaticResource TextButtonStyle}"
                            Margin="0,0,8,0"/>
                    <Button Content="⚙"
                            Style="{StaticResource IconButtonStyle}"
                            Margin="0,0,8,0"/>
                    <Button Content="+"
                            Style="{StaticResource FloatingActionButtonStyle}"/>
                </StackPanel>
                <!-- 设计系统演示卡片 -->
                <Border Background="{DynamicResource SurfaceBrush}"
                        BorderBrush="{DynamicResource BorderBrush}"
                        BorderThickness="{DynamicResource BorderThicknessThin}"
                        CornerRadius="{DynamicResource CardCornerRadius}"
                        Padding="{DynamicResource CardPadding}"
                        Margin="{DynamicResource ContentMargin}"
                        Effect="{DynamicResource CardShadow}">

                    <StackPanel>
                        <TextBlock Text="Fluent Design 设计系统"
                                   Style="{DynamicResource TitleLargeTextStyle}"
                                   HorizontalAlignment="Center"
                                   Margin="{DynamicResource ContentMarginSmall}"/>

                        <TextBlock Text="这个应用程序使用了完整的 Fluent Design 设计系统"
                                   Style="{DynamicResource BodyMediumTextStyle}"
                                   HorizontalAlignment="Center"
                                   Foreground="{DynamicResource TextSecondaryBrush}"
                                   Margin="{DynamicResource ContentMargin}"/>

                        <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                            <Button Content="显示消息"
                                    Command="{Binding ShowMessageCommand}"
                                    Background="{DynamicResource PrimaryBrush}"
                                    Foreground="{DynamicResource TextOnPrimaryBrush}"
                                    Padding="{DynamicResource ButtonPadding}"
                                    Margin="{DynamicResource ThicknessS}"
                                    BorderThickness="0"/>

                            <Button Content="退出应用程序"
                                    Command="{Binding ExitApplicationCommand}"
                                    Background="{DynamicResource SecondaryBrush}"
                                    Foreground="{DynamicResource TextOnSecondaryBrush}"
                                    Padding="{DynamicResource ButtonPadding}"
                                    Margin="{DynamicResource ThicknessS}"
                                    BorderThickness="0"/>
                        </StackPanel>
                    </StackPanel>
                </Border>

                <!-- 颜色演示 -->
                <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="{DynamicResource ContentMargin}">
                    <Border Width="60" Height="40"
                            Background="{DynamicResource PrimaryBrush}"
                            CornerRadius="{DynamicResource CornerRadiusS}"
                            Margin="{DynamicResource ThicknessXS}">
                        <TextBlock Text="Primary"
                                   Foreground="{DynamicResource TextOnPrimaryBrush}"
                                   HorizontalAlignment="Center"
                                   VerticalAlignment="Center"
                                   Style="{DynamicResource LabelSmallTextStyle}"/>
                    </Border>

                    <Border Width="60" Height="40"
                            Background="{DynamicResource SuccessBrush}"
                            CornerRadius="{DynamicResource CornerRadiusS}"
                            Margin="{DynamicResource ThicknessXS}">
                        <TextBlock Text="Success"
                                   Foreground="White"
                                   HorizontalAlignment="Center"
                                   VerticalAlignment="Center"
                                   Style="{DynamicResource LabelSmallTextStyle}"/>
                    </Border>

                    <Border Width="60" Height="40"
                            Background="{DynamicResource WarningBrush}"
                            CornerRadius="{DynamicResource CornerRadiusS}"
                            Margin="{DynamicResource ThicknessXS}">
                        <TextBlock Text="Warning"
                                   Foreground="Black"
                                   HorizontalAlignment="Center"
                                   VerticalAlignment="Center"
                                   Style="{DynamicResource LabelSmallTextStyle}"/>
                    </Border>

                    <Border Width="60" Height="40"
                            Background="{DynamicResource ErrorBrush}"
                            CornerRadius="{DynamicResource CornerRadiusS}"
                            Margin="{DynamicResource ThicknessXS}">
                        <TextBlock Text="Error"
                                   Foreground="White"
                                   HorizontalAlignment="Center"
                                   VerticalAlignment="Center"
                                   Style="{DynamicResource LabelSmallTextStyle}"/>
                    </Border>
                </StackPanel>
            </StackPanel>
        </Grid>

        <!-- 状态栏 -->
        <StatusBar Grid.Row="2">
            <StatusBarItem>
                <TextBlock Text="{Binding StatusMessage}"/>
            </StatusBarItem>
        </StatusBar>
    </Grid>
</Window>
