{"Version": 1, "WorkspaceRootPath": "D:\\08 AirMonitor\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{6D3386E9-CE92-45CA-9905-33FB9D29FCDD}|AirMonitor\\AirMonitor.csproj|d:\\08 airmonitor\\airmonitor\\views\\buttonstylesdemo.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{6D3386E9-CE92-45CA-9905-33FB9D29FCDD}|AirMonitor\\AirMonitor.csproj|solutionrelative:airmonitor\\views\\buttonstylesdemo.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}, {"AbsoluteMoniker": "D:0:0:{6D3386E9-CE92-45CA-9905-33FB9D29FCDD}|AirMonitor\\AirMonitor.csproj|d:\\08 airmonitor\\airmonitor\\mainwindow.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{6D3386E9-CE92-45CA-9905-33FB9D29FCDD}|AirMonitor\\AirMonitor.csproj|solutionrelative:airmonitor\\mainwindow.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}, {"AbsoluteMoniker": "D:0:0:{6D3386E9-CE92-45CA-9905-33FB9D29FCDD}|AirMonitor\\AirMonitor.csproj|d:\\08 airmonitor\\airmonitor\\styles\\buttonstyles.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{6D3386E9-CE92-45CA-9905-33FB9D29FCDD}|AirMonitor\\AirMonitor.csproj|solutionrelative:airmonitor\\styles\\buttonstyles.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}, {"AbsoluteMoniker": "D:0:0:{6D3386E9-CE92-45CA-9905-33FB9D29FCDD}|AirMonitor\\AirMonitor.csproj|d:\\08 airmonitor\\airmonitor\\themes\\colors\\colors.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{6D3386E9-CE92-45CA-9905-33FB9D29FCDD}|AirMonitor\\AirMonitor.csproj|solutionrelative:airmonitor\\themes\\colors\\colors.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}, {"AbsoluteMoniker": "D:0:0:{6D3386E9-CE92-45CA-9905-33FB9D29FCDD}|AirMonitor\\AirMonitor.csproj|d:\\08 airmonitor\\airmonitor\\themes\\typography\\typography.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{6D3386E9-CE92-45CA-9905-33FB9D29FCDD}|AirMonitor\\AirMonitor.csproj|solutionrelative:airmonitor\\themes\\typography\\typography.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}, {"AbsoluteMoniker": "D:0:0:{6D3386E9-CE92-45CA-9905-33FB9D29FCDD}|AirMonitor\\AirMonitor.csproj|d:\\08 airmonitor\\airmonitor\\views\\designsystemdemo.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{6D3386E9-CE92-45CA-9905-33FB9D29FCDD}|AirMonitor\\AirMonitor.csproj|solutionrelative:airmonitor\\views\\designsystemdemo.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}, {"AbsoluteMoniker": "D:0:0:{6D3386E9-CE92-45CA-9905-33FB9D29FCDD}|AirMonitor\\AirMonitor.csproj|d:\\08 airmonitor\\airmonitor\\themes\\dark.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{6D3386E9-CE92-45CA-9905-33FB9D29FCDD}|AirMonitor\\AirMonitor.csproj|solutionrelative:airmonitor\\themes\\dark.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}, {"AbsoluteMoniker": "D:0:0:{6D3386E9-CE92-45CA-9905-33FB9D29FCDD}|AirMonitor\\AirMonitor.csproj|d:\\08 airmonitor\\airmonitor\\themes\\light.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{6D3386E9-CE92-45CA-9905-33FB9D29FCDD}|AirMonitor\\AirMonitor.csproj|solutionrelative:airmonitor\\themes\\light.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 11, "Children": [{"$type": "Bookmark", "Name": "ST:130:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Bookmark", "Name": "ST:132:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Bookmark", "Name": "ST:131:0:{116d2292-e37d-41cd-a077-ebacac4c8cc4}"}, {"$type": "Bookmark", "Name": "ST:133:0:{116d2292-e37d-41cd-a077-ebacac4c8cc4}"}, {"$type": "Bookmark", "Name": "ST:134:0:{116d2292-e37d-41cd-a077-ebacac4c8cc4}"}, {"$type": "Bookmark", "Name": "ST:135:0:{116d2292-e37d-41cd-a077-ebacac4c8cc4}"}, {"$type": "Bookmark", "Name": "ST:0:0:{d84ee353-0bef-5a41-a649-8f89aca5d84d}"}, {"$type": "Bookmark", "Name": "ST:0:0:{aa2115a1-9712-457b-9047-dbb71ca2cdd2}"}, {"$type": "Document", "DocumentIndex": 1, "Title": "MainWindow.xaml", "DocumentMoniker": "D:\\08 AirMonitor\\AirMonitor\\MainWindow.xaml", "RelativeDocumentMoniker": "AirMonitor\\MainWindow.xaml", "ToolTip": "D:\\08 AirMonitor\\AirMonitor\\MainWindow.xaml", "RelativeToolTip": "AirMonitor\\MainWindow.xaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-06-19T09:14:16.853Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "Colors.xaml", "DocumentMoniker": "D:\\08 AirMonitor\\AirMonitor\\Themes\\Colors\\Colors.xaml", "RelativeDocumentMoniker": "AirMonitor\\Themes\\Colors\\Colors.xaml", "ToolTip": "D:\\08 AirMonitor\\AirMonitor\\Themes\\Colors\\Colors.xaml", "RelativeToolTip": "AirMonitor\\Themes\\Colors\\Colors.xaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-06-19T09:12:49.899Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 4, "Title": "Typography.xaml", "DocumentMoniker": "D:\\08 AirMonitor\\AirMonitor\\Themes\\Typography\\Typography.xaml", "RelativeDocumentMoniker": "AirMonitor\\Themes\\Typography\\Typography.xaml", "ToolTip": "D:\\08 AirMonitor\\AirMonitor\\Themes\\Typography\\Typography.xaml", "RelativeToolTip": "AirMonitor\\Themes\\Typography\\Typography.xaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-06-19T09:09:42.897Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 0, "Title": "ButtonStylesDemo.xaml", "DocumentMoniker": "D:\\08 AirMonitor\\AirMonitor\\Views\\ButtonStylesDemo.xaml", "RelativeDocumentMoniker": "AirMonitor\\Views\\ButtonStylesDemo.xaml", "ToolTip": "D:\\08 AirMonitor\\AirMonitor\\Views\\ButtonStylesDemo.xaml", "RelativeToolTip": "AirMonitor\\Views\\ButtonStylesDemo.xaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-06-19T09:08:44.978Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 5, "Title": "DesignSystemDemo.xaml", "DocumentMoniker": "D:\\08 AirMonitor\\AirMonitor\\Views\\DesignSystemDemo.xaml", "RelativeDocumentMoniker": "AirMonitor\\Views\\DesignSystemDemo.xaml", "ToolTip": "D:\\08 AirMonitor\\AirMonitor\\Views\\DesignSystemDemo.xaml", "RelativeToolTip": "AirMonitor\\Views\\DesignSystemDemo.xaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-06-19T09:07:46.262Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 6, "Title": "Dark.xaml", "DocumentMoniker": "D:\\08 AirMonitor\\AirMonitor\\Themes\\Dark.xaml", "RelativeDocumentMoniker": "AirMonitor\\Themes\\Dark.xaml", "ToolTip": "D:\\08 AirMonitor\\AirMonitor\\Themes\\Dark.xaml", "RelativeToolTip": "AirMonitor\\Themes\\Dark.xaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-06-19T09:06:56.982Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 7, "Title": "Light.xaml", "DocumentMoniker": "D:\\08 AirMonitor\\AirMonitor\\Themes\\Light.xaml", "RelativeDocumentMoniker": "AirMonitor\\Themes\\Light.xaml", "ToolTip": "D:\\08 AirMonitor\\AirMonitor\\Themes\\Light.xaml", "RelativeToolTip": "AirMonitor\\Themes\\Light.xaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-06-19T09:06:50.853Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "ButtonStyles.xaml", "DocumentMoniker": "D:\\08 AirMonitor\\AirMonitor\\Styles\\ButtonStyles.xaml", "RelativeDocumentMoniker": "AirMonitor\\Styles\\ButtonStyles.xaml", "ToolTip": "D:\\08 AirMonitor\\AirMonitor\\Styles\\ButtonStyles.xaml", "RelativeToolTip": "AirMonitor\\Styles\\ButtonStyles.xaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-06-19T09:03:39.052Z", "EditorCaption": ""}]}]}]}