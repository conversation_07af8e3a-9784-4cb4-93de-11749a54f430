<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <!-- ========================================
         FLUENT DESIGN SHADOW SYSTEM
         ======================================== -->

    <!-- Shadow Colors (Light Theme) -->
    <Color x:Key="ShadowColor">#1A000000</Color>
    <Color x:Key="ShadowColorMedium">#26000000</Color>
    <Color x:Key="ShadowColorStrong">#33000000</Color>
    <Color x:Key="ShadowColorDeep">#4D000000</Color>

    <!-- Shadow Brushes -->
    <SolidColorBrush x:Key="ShadowBrush" Color="{StaticResource ShadowColor}"/>
    <SolidColorBrush x:Key="ShadowMediumBrush" Color="{StaticResource ShadowColorMedium}"/>
    <SolidColorBrush x:Key="ShadowStrongBrush" Color="{StaticResource ShadowColorStrong}"/>
    <SolidColorBrush x:Key="ShadowDeepBrush" Color="{StaticResource ShadowColorDeep}"/>

    <!-- ========================================
         ELEVATION LEVELS (DROP SHADOW EFFECTS)
         ======================================== -->

    <!-- Elevation 1 - Subtle shadow for cards, buttons -->
    <DropShadowEffect x:Key="Elevation1Shadow"
                      Color="{StaticResource ShadowColor}"
                      Direction="270"
                      ShadowDepth="1"
                      BlurRadius="3"
                      Opacity="0.16"/>

    <!-- Elevation 2 - Medium shadow for floating elements -->
    <DropShadowEffect x:Key="Elevation2Shadow"
                      Color="{StaticResource ShadowColorMedium}"
                      Direction="270"
                      ShadowDepth="2"
                      BlurRadius="6"
                      Opacity="0.20"/>

    <!-- Elevation 3 - Prominent shadow for dialogs, menus -->
    <DropShadowEffect x:Key="Elevation3Shadow"
                      Color="{StaticResource ShadowColorStrong}"
                      Direction="270"
                      ShadowDepth="4"
                      BlurRadius="12"
                      Opacity="0.24"/>

    <!-- Elevation 4 - Strong shadow for modals, overlays -->
    <DropShadowEffect x:Key="Elevation4Shadow"
                      Color="{StaticResource ShadowColorStrong}"
                      Direction="270"
                      ShadowDepth="8"
                      BlurRadius="24"
                      Opacity="0.28"/>

    <!-- Elevation 5 - Deep shadow for top-level elements -->
    <DropShadowEffect x:Key="Elevation5Shadow"
                      Color="{StaticResource ShadowColorDeep}"
                      Direction="270"
                      ShadowDepth="16"
                      BlurRadius="48"
                      Opacity="0.32"/>

    <!-- ========================================
         SPECIALIZED SHADOW EFFECTS
         ======================================== -->

    <!-- Button Hover Shadow -->
    <DropShadowEffect x:Key="ButtonHoverShadow"
                      Color="{StaticResource ShadowColorMedium}"
                      Direction="270"
                      ShadowDepth="2"
                      BlurRadius="8"
                      Opacity="0.20"/>

    <!-- Button Pressed Shadow -->
    <DropShadowEffect x:Key="ButtonPressedShadow"
                      Color="{StaticResource ShadowColor}"
                      Direction="270"
                      ShadowDepth="1"
                      BlurRadius="2"
                      Opacity="0.12"/>

    <!-- Card Shadow -->
    <DropShadowEffect x:Key="CardShadow"
                      Color="{StaticResource ShadowColor}"
                      Direction="270"
                      ShadowDepth="1"
                      BlurRadius="4"
                      Opacity="0.16"/>

    <!-- Card Hover Shadow -->
    <DropShadowEffect x:Key="CardHoverShadow"
                      Color="{StaticResource ShadowColorMedium}"
                      Direction="270"
                      ShadowDepth="3"
                      BlurRadius="12"
                      Opacity="0.24"/>

    <!-- Popup Shadow -->
    <DropShadowEffect x:Key="PopupShadow"
                      Color="{StaticResource ShadowColorStrong}"
                      Direction="270"
                      ShadowDepth="6"
                      BlurRadius="20"
                      Opacity="0.28"/>

    <!-- Tooltip Shadow -->
    <DropShadowEffect x:Key="TooltipShadow"
                      Color="{StaticResource ShadowColorMedium}"
                      Direction="270"
                      ShadowDepth="2"
                      BlurRadius="8"
                      Opacity="0.20"/>

    <!-- Navigation Shadow -->
    <DropShadowEffect x:Key="NavigationShadow"
                      Color="{StaticResource ShadowColor}"
                      Direction="270"
                      ShadowDepth="0"
                      BlurRadius="8"
                      Opacity="0.12"/>

    <!-- ========================================
         GLOW EFFECTS
         ======================================== -->

    <!-- Primary Glow -->
    <DropShadowEffect x:Key="PrimaryGlow"
                      Color="#0078D4"
                      Direction="270"
                      ShadowDepth="0"
                      BlurRadius="8"
                      Opacity="0.4"/>

    <!-- Accent Glow -->
    <DropShadowEffect x:Key="AccentGlow"
                      Color="#FF6B35"
                      Direction="270"
                      ShadowDepth="0"
                      BlurRadius="8"
                      Opacity="0.4"/>

    <!-- Success Glow -->
    <DropShadowEffect x:Key="SuccessGlow"
                      Color="#107C10"
                      Direction="270"
                      ShadowDepth="0"
                      BlurRadius="8"
                      Opacity="0.4"/>

    <!-- Warning Glow -->
    <DropShadowEffect x:Key="WarningGlow"
                      Color="#FFB900"
                      Direction="270"
                      ShadowDepth="0"
                      BlurRadius="8"
                      Opacity="0.4"/>

    <!-- Error Glow -->
    <DropShadowEffect x:Key="ErrorGlow"
                      Color="#D13438"
                      Direction="270"
                      ShadowDepth="0"
                      BlurRadius="8"
                      Opacity="0.4"/>

    <!-- ========================================
         INNER SHADOW EFFECTS (Using Border)
         ======================================== -->

    <!-- Inner Shadow Border Style -->
    <Style x:Key="InnerShadowBorder" TargetType="Border">
        <Setter Property="Background">
            <Setter.Value>
                <LinearGradientBrush StartPoint="0,0" EndPoint="0,1">
                    <GradientStop Color="#08000000" Offset="0"/>
                    <GradientStop Color="#00000000" Offset="0.1"/>
                    <GradientStop Color="#00000000" Offset="1"/>
                </LinearGradientBrush>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- Inset Shadow Border Style -->
    <Style x:Key="InsetShadowBorder" TargetType="Border">
        <Setter Property="BorderBrush">
            <Setter.Value>
                <LinearGradientBrush StartPoint="0,0" EndPoint="0,1">
                    <GradientStop Color="#1A000000" Offset="0"/>
                    <GradientStop Color="#0D000000" Offset="0.5"/>
                    <GradientStop Color="#00000000" Offset="1"/>
                </LinearGradientBrush>
            </Setter.Value>
        </Setter>
        <Setter Property="BorderThickness" Value="1"/>
    </Style>

    <!-- ========================================
         SHADOW ANIMATION RESOURCES
         ======================================== -->

    <!-- Shadow Depth Animation -->
    <Storyboard x:Key="ElevateAnimation">
        <DoubleAnimation Storyboard.TargetProperty="(UIElement.Effect).(DropShadowEffect.ShadowDepth)"
                         To="4" Duration="0:0:0.2">
            <DoubleAnimation.EasingFunction>
                <CubicEase EasingMode="EaseOut"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
        <DoubleAnimation Storyboard.TargetProperty="(UIElement.Effect).(DropShadowEffect.BlurRadius)"
                         To="12" Duration="0:0:0.2">
            <DoubleAnimation.EasingFunction>
                <CubicEase EasingMode="EaseOut"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
    </Storyboard>

    <!-- Shadow Lower Animation -->
    <Storyboard x:Key="LowerAnimation">
        <DoubleAnimation Storyboard.TargetProperty="(UIElement.Effect).(DropShadowEffect.ShadowDepth)"
                         To="1" Duration="0:0:0.2">
            <DoubleAnimation.EasingFunction>
                <CubicEase EasingMode="EaseOut"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
        <DoubleAnimation Storyboard.TargetProperty="(UIElement.Effect).(DropShadowEffect.BlurRadius)"
                         To="4" Duration="0:0:0.2">
            <DoubleAnimation.EasingFunction>
                <CubicEase EasingMode="EaseOut"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
    </Storyboard>

</ResourceDictionary>
