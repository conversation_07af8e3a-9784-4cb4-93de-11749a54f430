using System;
using System.Collections.Generic;
using System.Windows;
using Microsoft.Extensions.Logging;
using CommunityToolkit.Mvvm.ComponentModel;

namespace AirMonitor.Services.Themes;

/// <summary>
/// 主题类型枚举
/// </summary>
public enum ThemeType
{
    Light,
    Dark,
    System
}

/// <summary>
/// 主题管理器，负责管理和切换应用程序主题
/// </summary>
public partial class ThemeManager : ObservableObject, IThemeManager
{
    private readonly ILogger<ThemeManager> _logger;

    /// <summary>
    /// 当前主题类型
    /// </summary>
    [ObservableProperty]
    private ThemeType currentTheme;

    /// <summary>
    /// 是否为暗色主题
    /// </summary>
    [ObservableProperty]
    private bool isDarkTheme;

    /// <summary>
    /// 主题变更事件
    /// </summary>
    public event EventHandler<ThemeChangedEventArgs>? ThemeChanged;

    public ThemeManager(ILogger<ThemeManager> logger)
    {
        _logger = logger;
        
        // 初始化时检测系统主题
        DetectSystemTheme();
        
        // 监听系统主题变化
        SystemEvents.UserPreferenceChanged += OnSystemPreferenceChanged;
    }

    /// <summary>
    /// 应用主题
    /// </summary>
    /// <param name="theme">主题类型</param>
    public void ApplyTheme(ThemeType theme)
    {
        try
        {
            var previousTheme = CurrentTheme;
            CurrentTheme = theme;

            var actualTheme = GetActualTheme(theme);
            IsDarkTheme = actualTheme == ThemeType.Dark;

            // 清除现有主题资源
            ClearThemeResources();

            // 应用新主题资源
            ApplyThemeResources(actualTheme);

            _logger.LogInformation("主题已切换: {PreviousTheme} -> {NewTheme} (实际: {ActualTheme})", 
                previousTheme, theme, actualTheme);

            // 触发主题变更事件
            ThemeChanged?.Invoke(this, new ThemeChangedEventArgs(previousTheme, theme, actualTheme));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "应用主题时发生错误: {Theme}", theme);
            throw;
        }
    }

    /// <summary>
    /// 切换主题
    /// </summary>
    public void ToggleTheme()
    {
        var newTheme = CurrentTheme switch
        {
            ThemeType.Light => ThemeType.Dark,
            ThemeType.Dark => ThemeType.Light,
            ThemeType.System => IsDarkTheme ? ThemeType.Light : ThemeType.Dark,
            _ => ThemeType.Light
        };

        ApplyTheme(newTheme);
    }

    /// <summary>
    /// 获取实际主题类型（处理系统主题）
    /// </summary>
    /// <param name="theme">主题类型</param>
    /// <returns>实际主题类型</returns>
    private ThemeType GetActualTheme(ThemeType theme)
    {
        if (theme == ThemeType.System)
        {
            return IsSystemDarkTheme() ? ThemeType.Dark : ThemeType.Light;
        }
        return theme;
    }

    /// <summary>
    /// 检测系统主题
    /// </summary>
    private void DetectSystemTheme()
    {
        if (CurrentTheme == ThemeType.System)
        {
            IsDarkTheme = IsSystemDarkTheme();
        }
    }

    /// <summary>
    /// 判断系统是否为暗色主题
    /// </summary>
    /// <returns>是否为暗色主题</returns>
    private bool IsSystemDarkTheme()
    {
        try
        {
            using var key = Microsoft.Win32.Registry.CurrentUser.OpenSubKey(
                @"Software\Microsoft\Windows\CurrentVersion\Themes\Personalize");
            
            var value = key?.GetValue("AppsUseLightTheme");
            return value is int intValue && intValue == 0;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "无法检测系统主题，使用默认亮色主题");
            return false;
        }
    }

    /// <summary>
    /// 系统偏好设置变更事件处理
    /// </summary>
    private void OnSystemPreferenceChanged(object sender, Microsoft.Win32.UserPreferenceChangedEventArgs e)
    {
        if (e.Category == Microsoft.Win32.UserPreferenceCategory.General && CurrentTheme == ThemeType.System)
        {
            Application.Current.Dispatcher.BeginInvoke(() =>
            {
                DetectSystemTheme();
                ApplyTheme(ThemeType.System);
            });
        }
    }

    /// <summary>
    /// 清除主题资源
    /// </summary>
    private void ClearThemeResources()
    {
        var app = Application.Current;
        if (app?.Resources == null) return;

        // 移除现有主题资源字典
        var resourcesToRemove = new List<ResourceDictionary>();
        
        foreach (ResourceDictionary resource in app.Resources.MergedDictionaries)
        {
            if (IsThemeResource(resource))
            {
                resourcesToRemove.Add(resource);
            }
        }

        foreach (var resource in resourcesToRemove)
        {
            app.Resources.MergedDictionaries.Remove(resource);
        }
    }

    /// <summary>
    /// 应用主题资源
    /// </summary>
    /// <param name="theme">主题类型</param>
    private void ApplyThemeResources(ThemeType theme)
    {
        var app = Application.Current;
        if (app?.Resources == null) return;

        try
        {
            // 基础设计系统资源（通用）
            AddResourceDictionary("pack://application:,,,/Themes/Spacing/Spacing.xaml");
            AddResourceDictionary("pack://application:,,,/Themes/BorderRadius/BorderRadius.xaml");
            AddResourceDictionary("pack://application:,,,/Themes/Typography/Typography.xaml");

            // 主题特定资源
            if (theme == ThemeType.Dark)
            {
                AddResourceDictionary("pack://application:,,,/Themes/Colors/ColorsDark.xaml");
                AddResourceDictionary("pack://application:,,,/Themes/Shadows/ShadowsDark.xaml");
            }
            else
            {
                AddResourceDictionary("pack://application:,,,/Themes/Colors/Colors.xaml");
                AddResourceDictionary("pack://application:,,,/Themes/Shadows/Shadows.xaml");
            }

            _logger.LogDebug("主题资源已应用: {Theme}", theme);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "应用主题资源时发生错误: {Theme}", theme);
            throw;
        }
    }

    /// <summary>
    /// 添加资源字典
    /// </summary>
    /// <param name="uri">资源字典URI</param>
    private void AddResourceDictionary(string uri)
    {
        try
        {
            var resourceDict = new ResourceDictionary
            {
                Source = new Uri(uri, UriKind.RelativeOrAbsolute)
            };
            
            Application.Current.Resources.MergedDictionaries.Add(resourceDict);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "加载资源字典失败: {Uri}", uri);
            throw;
        }
    }

    /// <summary>
    /// 判断是否为主题资源
    /// </summary>
    /// <param name="resource">资源字典</param>
    /// <returns>是否为主题资源</returns>
    private bool IsThemeResource(ResourceDictionary resource)
    {
        if (resource.Source == null) return false;
        
        var uri = resource.Source.ToString();
        return uri.Contains("/Themes/") || 
               uri.Contains("Colors") || 
               uri.Contains("Shadows") || 
               uri.Contains("Typography") || 
               uri.Contains("Spacing") || 
               uri.Contains("BorderRadius");
    }

    /// <summary>
    /// 释放资源
    /// </summary>
    public void Dispose()
    {
        SystemEvents.UserPreferenceChanged -= OnSystemPreferenceChanged;
        GC.SuppressFinalize(this);
    }
}

/// <summary>
/// 主题变更事件参数
/// </summary>
public class ThemeChangedEventArgs : EventArgs
{
    public ThemeType PreviousTheme { get; }
    public ThemeType NewTheme { get; }
    public ThemeType ActualTheme { get; }

    public ThemeChangedEventArgs(ThemeType previousTheme, ThemeType newTheme, ThemeType actualTheme)
    {
        PreviousTheme = previousTheme;
        NewTheme = newTheme;
        ActualTheme = actualTheme;
    }
}

/// <summary>
/// 系统事件类（简化版）
/// </summary>
public static class SystemEvents
{
    public static event Microsoft.Win32.UserPreferenceChangedEventHandler? UserPreferenceChanged
    {
        add => Microsoft.Win32.SystemEvents.UserPreferenceChanged += value;
        remove => Microsoft.Win32.SystemEvents.UserPreferenceChanged -= value;
    }
}
