# Fluent Design 设计系统

这是一个完整的 Fluent Design 设计系统，为 WPF 应用程序提供一致的视觉体验和用户界面组件。

## 📁 文件结构

```
Themes/
├── Colors/                 # 色彩系统
│   ├── Colors.xaml        # 亮色主题颜色
│   └── ColorsDark.xaml    # 暗色主题颜色
├── Typography/            # 字体系统
│   └── Typography.xaml    # 字体样式和层级
├── Shadows/               # 阴影系统
│   ├── Shadows.xaml       # 亮色主题阴影
│   └── ShadowsDark.xaml   # 暗色主题阴影
├── Spacing/               # 间距系统
│   └── Spacing.xaml       # 8pt网格间距规范
├── BorderRadius/          # 圆角系统
│   └── BorderRadius.xaml  # 圆角半径规范
├── Light.xaml             # 亮色主题整合
├── Dark.xaml              # 暗色主题整合
└── README.md              # 本文档
```

## 🎨 色彩系统

### 主要颜色
- **Primary**: `#0078D4` - 主要品牌色
- **Secondary**: `#6B73FF` - 次要品牌色
- **Accent**: `#FF6B35` - 强调色

### 语义化颜色
- **Success**: `#107C10` - 成功状态
- **Warning**: `#FFB900` - 警告状态
- **Error**: `#D13438` - 错误状态
- **Info**: `#0078D4` - 信息状态

### 使用示例
```xml
<!-- 使用主要颜色 -->
<Button Background="{StaticResource PrimaryBrush}" 
        Foreground="{StaticResource TextOnPrimaryBrush}"/>

<!-- 使用语义化颜色 -->
<Border Background="{StaticResource SuccessBrush}"/>
```

## 📝 字体系统

### 字体层级
- **Display Large**: 56px - 大标题
- **Display Medium**: 48px - 中标题
- **Display Small**: 40px - 小标题
- **Headline Large**: 32px - H1
- **Headline Medium**: 28px - H2
- **Headline Small**: 24px - H3
- **Title Large**: 20px - H4
- **Title Medium**: 18px - H5
- **Title Small**: 16px - H6
- **Body Large**: 16px - 正文大
- **Body Medium**: 14px - 正文中
- **Body Small**: 12px - 正文小

### 使用示例
```xml
<!-- 使用标题样式 -->
<TextBlock Text="主标题" Style="{StaticResource HeadlineLargeTextStyle}"/>

<!-- 使用正文样式 -->
<TextBlock Text="正文内容" Style="{StaticResource BodyMediumTextStyle}"/>
```

## 🌫️ 阴影系统

### 阴影层级
- **Elevation 1**: 轻微阴影 - 卡片、按钮
- **Elevation 2**: 中等阴影 - 浮动元素
- **Elevation 3**: 明显阴影 - 对话框、菜单
- **Elevation 4**: 强烈阴影 - 模态框、覆盖层
- **Elevation 5**: 深度阴影 - 顶层元素

### 使用示例
```xml
<!-- 应用卡片阴影 -->
<Border Effect="{StaticResource CardShadow}"/>

<!-- 应用按钮悬停阴影 -->
<Button Effect="{StaticResource ButtonHoverShadow}"/>
```

## 📏 间距系统

基于 8pt 网格系统：

### 间距值
- **XS**: 2px
- **S**: 8px
- **M**: 16px
- **L**: 24px
- **XL**: 40px

### 使用示例
```xml
<!-- 使用标准间距 -->
<StackPanel Margin="{StaticResource ThicknessM}">
    <Button Margin="{StaticResource ThicknessS}"/>
</StackPanel>

<!-- 使用组件特定间距 -->
<Button Padding="{StaticResource ButtonPadding}"/>
```

## 🔘 圆角系统

### 圆角级别
- **None**: 0px - 无圆角
- **S**: 4px - 小圆角
- **M**: 6px - 中圆角
- **L**: 12px - 大圆角
- **XL**: 16px - 超大圆角
- **Circular**: 9999px - 圆形

### 使用示例
```xml
<!-- 使用标准圆角 -->
<Border CornerRadius="{StaticResource CornerRadiusM}"/>

<!-- 使用按钮圆角 -->
<Button Style="{StaticResource ButtonBorderStyle}"/>
```

## 🎭 主题管理

### 主题类型
- **Light**: 亮色主题
- **Dark**: 暗色主题
- **System**: 跟随系统主题

### 使用主题管理器
```csharp
// 注入主题管理器
public MainWindow(IThemeManager themeManager)
{
    _themeManager = themeManager;
}

// 切换主题
_themeManager.ApplyTheme(ThemeType.Dark);

// 切换主题（自动）
_themeManager.ToggleTheme();

// 监听主题变化
_themeManager.ThemeChanged += OnThemeChanged;
```

## 🚀 快速开始

### 1. 在 App.xaml 中引用主题
```xml
<Application.Resources>
    <ResourceDictionary>
        <ResourceDictionary.MergedDictionaries>
            <ResourceDictionary Source="Themes/Light.xaml"/>
        </ResourceDictionary.MergedDictionaries>
    </ResourceDictionary>
</Application.Resources>
```

### 2. 注册主题管理器
```csharp
services.AddSingleton<IThemeManager, ThemeManager>();
```

### 3. 初始化主题
```csharp
var themeManager = serviceProvider.GetRequiredService<IThemeManager>();
themeManager.ApplyTheme(ThemeType.System);
```

## 📋 最佳实践

### 1. 使用动态资源
```xml
<!-- 推荐：使用动态资源，支持主题切换 -->
<Button Background="{DynamicResource PrimaryBrush}"/>

<!-- 不推荐：使用静态资源，不支持主题切换 -->
<Button Background="{StaticResource PrimaryBrush}"/>
```

### 2. 遵循命名约定
- 颜色资源以 `Color` 结尾
- 画刷资源以 `Brush` 结尾
- 样式资源以 `Style` 结尾
- 厚度资源以 `Thickness` 开头

### 3. 使用语义化命名
```xml
<!-- 推荐：语义化命名 -->
<Border Background="{DynamicResource SuccessBrush}"/>

<!-- 不推荐：具体颜色值 -->
<Border Background="#107C10"/>
```

### 4. 组件特定资源
```xml
<!-- 使用组件特定的资源 -->
<Button Padding="{StaticResource ButtonPadding}"
        CornerRadius="{StaticResource ButtonCornerRadius}"/>
```

## 🔧 自定义扩展

### 添加新颜色
在 `Colors.xaml` 和 `ColorsDark.xaml` 中添加：
```xml
<Color x:Key="CustomColor">#FF5722</Color>
<SolidColorBrush x:Key="CustomBrush" Color="{StaticResource CustomColor}"/>
```

### 添加新字体样式
在 `Typography.xaml` 中添加：
```xml
<Style x:Key="CustomTextStyle" TargetType="TextBlock">
    <Setter Property="FontSize" Value="18"/>
    <Setter Property="FontWeight" Value="Medium"/>
</Style>
```

### 添加新阴影效果
在 `Shadows.xaml` 中添加：
```xml
<DropShadowEffect x:Key="CustomShadow"
                  Color="#1A000000"
                  Direction="270"
                  ShadowDepth="3"
                  BlurRadius="8"/>
```

## 📱 响应式设计

设计系统包含响应式间距变量：
```xml
<!-- 移动端间距 -->
<sys:Double x:Key="MobileSpacingM">8</sys:Double>

<!-- 桌面端间距 -->
<sys:Double x:Key="DesktopSpacingM">16</sys:Double>
```

## 🎯 性能优化

1. **资源预加载**: 主题资源在应用启动时加载
2. **动态切换**: 支持运行时主题切换，无需重启
3. **内存管理**: 主题管理器自动清理未使用的资源
4. **缓存机制**: 资源字典缓存，避免重复加载

## 🐛 故障排除

### 主题不生效
1. 检查资源字典是否正确引用
2. 确保使用 `DynamicResource` 而不是 `StaticResource`
3. 验证主题管理器是否正确初始化

### 颜色显示异常
1. 检查暗色主题和亮色主题是否都定义了相同的资源键
2. 验证颜色值格式是否正确
3. 确保没有硬编码颜色值

### 性能问题
1. 避免频繁切换主题
2. 使用合适的资源类型（静态 vs 动态）
3. 检查是否有资源泄漏

---

## 📄 许可证

本设计系统遵循项目许可证。

## 🤝 贡献

欢迎提交 Issue 和 Pull Request 来改进设计系统。
