using CommunityToolkit.Mvvm.ComponentModel;

namespace AirMonitor.ViewModels;

/// <summary>
/// ViewModel基类，提供通用的属性变更通知功能
/// </summary>
public abstract partial class ViewModelBase : ObservableObject
{
    /// <summary>
    /// 标题属性
    /// </summary>
    [ObservableProperty]
    private string _title = string.Empty;

    /// <summary>
    /// 是否忙碌状态
    /// </summary>
    [ObservableProperty]
    private bool _isBusy;

    /// <summary>
    /// 状态消息
    /// </summary>
    [ObservableProperty]
    private string _statusMessage = string.Empty;
}
